# 汉典转macOS词典转换程序优化计划 - 第二步

## 优化目标

基于第一步的成功基础，第二步优化将专注于：

1. **资源文件处理优化**：改进图片和音频文件的处理逻辑
2. **性能优化**：提升处理速度和内存使用效率
3. **内容提取增强**：进一步改进HTML解析和内容识别
4. **错误处理完善**：增强异常处理和恢复机制

## 当前问题分析

### 1. 资源文件处理问题
从测试日志中发现的问题：
- 图片文件路径解析不准确（如：`src=` 空值）
- 音频文件路径提取错误（如：`title=` 作为路径）
- 部分资源文件缺失或路径错误

### 2. HTML内容解析问题
- 仍有部分HTML内容解析失败，回退到纯文本
- 复杂HTML结构处理不够完善
- 特殊字符和实体处理需要进一步优化

### 3. 性能优化空间
- 并行处理可以进一步优化
- 内存使用可以更加高效
- 文件I/O操作可以批量化

## 第二步优化方案

### 1. 资源文件处理优化

#### 1.1 改进图片处理逻辑
```python
def _extract_image_sources(self, soup):
    """改进的图片源提取方法"""
    image_sources = []
    
    # 查找所有img标签
    for img in soup.find_all('img'):
        src = None
        
        # 优先级顺序：src > data-original-src > data-src
        if img.get('src'):
            src = img.get('src')
        elif img.get('data-original-src'):
            src = img.get('data-original-src')
        elif img.get('data-src'):
            src = img.get('data-src')
        
        if src and src.strip():
            # 清理和标准化URL
            src = self._normalize_resource_url(src)
            if src:
                image_sources.append(src)
    
    return image_sources

def _normalize_resource_url(self, url):
    """标准化资源URL"""
    if not url or not url.strip():
        return None
    
    url = url.strip()
    
    # 移除无效的URL
    invalid_patterns = [
        r'^style=',
        r'^class=',
        r'^title=',
        r'^alt=',
        r'^\s*$'
    ]
    
    for pattern in invalid_patterns:
        if re.match(pattern, url, re.IGNORECASE):
            return None
    
    # 处理相对路径
    if url.startswith('../../'):
        url = url[6:]  # 移除 ../../
    elif url.startswith('../'):
        url = url[3:]   # 移除 ../
    elif url.startswith('./'):
        url = url[2:]   # 移除 ./
    
    # 确保URL格式正确
    if url.startswith('http'):
        return url
    elif url.startswith('/'):
        return f"https://img.zdic.net{url}"
    else:
        return f"https://img.zdic.net/{url}"
```

#### 1.2 改进音频处理逻辑
```python
def _extract_audio_sources(self, soup):
    """改进的音频源提取方法"""
    audio_sources = []
    
    # 查找audio标签
    for audio in soup.find_all('audio'):
        src = audio.get('src')
        if src and src.strip():
            src = self._normalize_resource_url(src)
            if src:
                audio_sources.append(src)
    
    # 查找带音频链接的元素
    audio_elements = soup.find_all(attrs={'data-src-mp3': True})
    for element in audio_elements:
        src = element.get('data-src-mp3')
        if src and src.strip():
            src = self._normalize_resource_url(src)
            if src:
                audio_sources.append(src)
    
    # 查找音频播放按钮
    play_buttons = soup.find_all(class_='audio_play_button')
    for button in play_buttons:
        src = button.get('data-src-mp3')
        if src and src.strip():
            src = self._normalize_resource_url(src)
            if src:
                audio_sources.append(src)
    
    return list(set(audio_sources))  # 去重
```

### 2. HTML内容解析增强

#### 2.1 改进HTML解析策略
```python
def _parse_html_content_enhanced(self, content):
    """增强的HTML内容解析"""
    if not content:
        return content
    
    try:
        # 使用多种解析器尝试
        parsers = ['html.parser', 'lxml', 'html5lib']
        
        for parser in parsers:
            try:
                soup = BeautifulSoup(content, parser)
                # 验证解析结果
                if self._validate_parsed_content(soup):
                    return self._process_parsed_content(soup)
            except Exception:
                continue
        
        # 如果所有解析器都失败，使用文本清理
        return self._fallback_text_processing(content)
        
    except Exception as e:
        logger.warning(f"HTML解析失败，使用纯文本: {e}")
        return self._fallback_text_processing(content)

def _validate_parsed_content(self, soup):
    """验证解析内容的质量"""
    # 检查是否有有效的文本内容
    text = soup.get_text().strip()
    if not text:
        return False
    
    # 检查是否保留了重要的结构
    important_tags = soup.find_all(['p', 'div', 'span', 'li', 'strong', 'em'])
    if not important_tags:
        return False
    
    return True

def _process_parsed_content(self, soup):
    """处理解析后的内容"""
    # 清理无用的属性
    for tag in soup.find_all():
        # 保留重要属性
        important_attrs = ['class', 'id', 'href', 'src', 'alt', 'title']
        attrs_to_remove = []
        
        for attr in tag.attrs:
            if attr not in important_attrs:
                attrs_to_remove.append(attr)
        
        for attr in attrs_to_remove:
            del tag[attr]
    
    # 转换为字符串并清理
    content = str(soup)
    content = self._clean_html_content(content)
    
    return content
```

### 3. 性能优化

#### 3.1 并行处理优化
```python
def _process_characters_optimized(self, characters, max_workers=None):
    """优化的并行处理"""
    if max_workers is None:
        max_workers = min(32, (os.cpu_count() or 1) + 4)
    
    # 按文件大小分组，平衡负载
    character_groups = self._balance_workload(characters)
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 使用批处理减少线程开销
        futures = []
        for group in character_groups:
            future = executor.submit(self._process_character_batch, group)
            futures.append(future)
        
        # 收集结果
        results = []
        for future in as_completed(futures):
            try:
                batch_results = future.result()
                results.extend(batch_results)
            except Exception as e:
                logger.error(f"批处理失败: {e}")
        
        return results

def _balance_workload(self, characters):
    """平衡工作负载"""
    # 根据文件大小估算处理时间
    character_weights = []
    for char in characters:
        weight = self._estimate_processing_weight(char)
        character_weights.append((char, weight))
    
    # 按权重排序
    character_weights.sort(key=lambda x: x[1], reverse=True)
    
    # 分组
    num_groups = min(len(characters), os.cpu_count() or 4)
    groups = [[] for _ in range(num_groups)]
    group_weights = [0] * num_groups
    
    for char, weight in character_weights:
        # 选择权重最小的组
        min_group_idx = group_weights.index(min(group_weights))
        groups[min_group_idx].append(char)
        group_weights[min_group_idx] += weight
    
    return [group for group in groups if group]
```

#### 3.2 内存优化
```python
def _process_with_memory_optimization(self, characters):
    """内存优化的处理方法"""
    # 使用生成器减少内存占用
    def character_generator():
        for char in characters:
            yield self._process_single_character(char)
            # 强制垃圾回收
            gc.collect()
    
    # 批量写入减少I/O
    batch_size = 100
    batch = []
    
    for result in character_generator():
        if result:
            batch.append(result)
            
            if len(batch) >= batch_size:
                self._write_batch_results(batch)
                batch.clear()
    
    # 写入剩余结果
    if batch:
        self._write_batch_results(batch)
```

### 4. 错误处理完善

#### 4.1 增强异常处理
```python
def _robust_character_processing(self, char_name):
    """健壮的字符处理"""
    max_retries = 3
    retry_delay = 1
    
    for attempt in range(max_retries):
        try:
            return self._process_character_with_validation(char_name)
        
        except FileNotFoundError as e:
            logger.warning(f"文件未找到 {char_name}: {e}")
            return None
        
        except MemoryError as e:
            logger.error(f"内存不足处理 {char_name}: {e}")
            gc.collect()  # 强制垃圾回收
            if attempt < max_retries - 1:
                time.sleep(retry_delay * (attempt + 1))
                continue
            return None
        
        except Exception as e:
            logger.error(f"处理 {char_name} 时出错 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
            
            # 最后尝试简化处理
            try:
                return self._fallback_simple_processing(char_name)
            except Exception as fallback_error:
                logger.error(f"简化处理也失败 {char_name}: {fallback_error}")
                return None
    
    return None
```

## 实施计划

### 阶段1：资源文件处理优化（1-2天）
1. 实现改进的图片和音频提取逻辑
2. 添加资源URL验证和清理
3. 测试资源文件处理准确性

### 阶段2：HTML解析增强（1-2天）
1. 实现多解析器策略
2. 添加内容验证机制
3. 改进回退处理逻辑

### 阶段3：性能优化（2-3天）
1. 实现负载平衡的并行处理
2. 添加内存优化机制
3. 优化文件I/O操作

### 阶段4：错误处理完善（1天）
1. 增强异常处理机制
2. 添加重试和回退策略
3. 完善日志记录

### 阶段5：集成测试（1天）
1. 大规模测试优化效果
2. 性能基准测试
3. 质量验证

## 预期效果

1. **资源文件处理准确率**：从当前的~80%提升到95%+
2. **HTML解析成功率**：从当前的~90%提升到98%+
3. **处理速度**：提升30-50%
4. **内存使用**：减少20-30%
5. **错误恢复能力**：显著增强

通过第二步优化，我们将进一步完善转换程序，确保能够处理更复杂的内容，提供更高的性能和稳定性。
