#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
转换模块
将提取的字符信息转换为macOS词典XML格式
"""

import os
import re
import logging
from typing import Dict, List, Any, Optional
from lxml import etree

from modules.config import (
    XML_ENCODING,
    XML_VERSION,
    XML_DOCTYPE,
    XML_NAMESPACE
)

# 获取logger
logger = logging.getLogger('zdic_converter')

class Converter:
    """转换器类"""

    def __init__(self):
        """初始化转换器"""
        # 注册命名空间
        self.nsmap = {
            'd': XML_NAMESPACE,
            None: 'http://www.w3.org/1999/xhtml'
        }

        # 创建XML解析器
        self.parser = etree.XMLParser(remove_blank_text=True)

    def create_dictionary_xml(self, entries: List[str], output_path: str) -> bool:
        """创建完整的词典XML文件

        Args:
            entries: 条目XML字符串列表
            output_path: 输出文件路径

        Returns:
            是否成功创建
        """
        try:
            # 创建根元素
            root = etree.Element(f"{{{XML_NAMESPACE}}}dictionary", nsmap=self.nsmap)

            # 添加每个条目
            for entry_xml in entries:
                try:
                    # 清理XML字符串，移除XML声明
                    cleaned_xml = self._clean_xml_string(entry_xml)

                    # 解析条目XML - 使用bytes而不是字符串来避免编码声明错误
                    entry_elem = etree.fromstring(cleaned_xml.encode('utf-8'), self.parser)
                    # 将条目添加到根元素
                    root.append(entry_elem)
                except Exception as e:
                    logger.error(f"解析条目XML时出错: {str(e)}")
                    # 尝试修复并重新解析
                    try:
                        # 进一步清理XML内容
                        fixed_xml = self._fix_xml_content(entry_xml)
                        entry_elem = etree.fromstring(fixed_xml.encode('utf-8'), self.parser)
                        root.append(entry_elem)
                        logger.info(f"成功修复并解析XML条目")
                    except Exception as e2:
                        logger.error(f"修复XML条目失败: {str(e2)}")
                        continue

            # 生成XML字符串
            xml_str = f'''<?xml version="{XML_VERSION}" encoding="{XML_ENCODING}"?>
{XML_DOCTYPE}
'''
            # 转换为字符串并添加到前面的声明
            root_str = etree.tostring(
                root,
                encoding=XML_ENCODING,
                pretty_print=True,
                xml_declaration=False
            ).decode(XML_ENCODING)

            xml_str += root_str

            # 写入文件
            with open(output_path, 'w', encoding=XML_ENCODING) as f:
                f.write(xml_str)

            logger.info(f"成功创建词典XML文件: {output_path}")
            return True

        except Exception as e:
            logger.error(f"创建词典XML文件时出错: {str(e)}")
            logger.exception(e)
            return False

    def _clean_xml_string(self, xml_string: str) -> str:
        """清理XML字符串，移除可能导致问题的内容

        Args:
            xml_string: 原始XML字符串

        Returns:
            清理后的XML字符串
        """
        # 移除XML声明
        xml_string = re.sub(r'<\?xml[^>]*\?>', '', xml_string)

        # 移除DOCTYPE声明
        xml_string = re.sub(r'<!DOCTYPE[^>]*>', '', xml_string)

        # 清理多余的空白字符
        xml_string = re.sub(r'\s+', ' ', xml_string).strip()

        return xml_string

    def _fix_xml_content(self, xml_string: str) -> str:
        """修复XML内容中的常见问题

        Args:
            xml_string: 原始XML字符串

        Returns:
            修复后的XML字符串
        """
        # 首先进行基本清理
        xml_string = self._clean_xml_string(xml_string)

        # 修复HTML实体编码问题
        xml_string = xml_string.replace('&lt;', '<')
        xml_string = xml_string.replace('&gt;', '>')
        xml_string = xml_string.replace('&quot;', '"')
        xml_string = xml_string.replace('&apos;', "'")
        xml_string = xml_string.replace('&amp;', '&')  # 最后处理&amp;

        # 修复无效的XML字符
        xml_string = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', xml_string)

        # 修复未闭合的标签（简单处理）
        xml_string = re.sub(r'<([^/>]+)(?<!/)>([^<]*)</(?!\1)', r'<\1>\2</\1>', xml_string)

        # 修复属性值中的引号问题
        xml_string = re.sub(r'(\w+)=([^"\s>]+)(?=\s|>)', r'\1="\2"', xml_string)

        return xml_string

    def _clean_html_content(self, content: str) -> str:
        """清理HTML内容

        Args:
            content: 原始HTML内容

        Returns:
            清理后的HTML内容
        """
        if not content:
            return content

        # 解码HTML实体
        content = self._decode_html_entities(content)

        # 修复常见的HTML问题
        # 修复未闭合的标签
        content = re.sub(r'<br\s*/?>', '<br/>', content)
        content = re.sub(r'<hr\s*/?>', '<hr/>', content)
        content = re.sub(r'<img([^>]*?)(?<!/)>', r'<img\1/>', content)

        # 修复属性值中的引号问题
        content = re.sub(r'(\w+)=([^"\s>]+)(?=\s|>)', r'\1="\2"', content)

        # 移除无效的XML字符
        content = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', content)

        return content

    def _decode_html_entities(self, text: str) -> str:
        """解码HTML实体

        Args:
            text: 包含HTML实体的文本

        Returns:
            解码后的文本
        """
        if not text:
            return text

        # 解码常见的HTML实体
        entity_map = {
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&apos;': "'",
            '&nbsp;': ' ',
            '&amp;': '&'  # 最后处理&amp;
        }

        for entity, char in entity_map.items():
            text = text.replace(entity, char)

        # 解码数字实体
        text = re.sub(r'&#(\d+);', lambda m: chr(int(m.group(1))), text)
        text = re.sub(r'&#x([0-9a-fA-F]+);', lambda m: chr(int(m.group(1), 16)), text)

        return text

    def convert_to_xml(self, char_data: Dict[str, Any]) -> str:
        """将字符数据转换为XML格式

        Args:
            char_data: 字符数据字典

        Returns:
            XML字符串
        """
        try:
            # 获取字符
            character = char_data.get('character', '')
            if not character:
                raise ValueError("字符数据中缺少character字段")

            # 创建根元素
            entry = etree.Element(f"{{{XML_NAMESPACE}}}entry", nsmap=self.nsmap)
            entry.set('id', f"zdic_{character}")
            entry.set(f"{{{XML_NAMESPACE}}}title", character)

            # 添加索引
            self._add_indexes(entry, char_data)

            # 创建内容div
            content_div = etree.SubElement(entry, f"{{{XML_NAMESPACE}}}div")
            content_div.set('class', 'entry')

            # 添加标题
            self._add_title(content_div, char_data)

            try:
                # 添加发音
                self._add_pronunciation(content_div, char_data)
            except Exception as e:
                logger.error(f"添加发音时出错: {str(e)}")

            try:
                # 添加部首和笔画信息
                self._add_radical_info(content_div, char_data)
            except Exception as e:
                logger.error(f"添加部首和笔画信息时出错: {str(e)}")

            try:
                # 添加基本释义
                self._add_basic_definitions(content_div, char_data)
            except Exception as e:
                logger.error(f"添加基本释义时出错: {str(e)}")

            try:
                # 添加详细释义
                self._add_detailed_definitions(content_div, char_data)
            except Exception as e:
                logger.error(f"添加详细释义时出错: {str(e)}")

            try:
                # 添加国语词典内容
                self._add_gycd_content(content_div, char_data)
            except Exception as e:
                logger.error(f"添加国语词典内容时出错: {str(e)}")

            try:
                # 添加康熙字典内容
                self._add_kxzd_content(content_div, char_data)
            except Exception as e:
                logger.error(f"添加康熙字典内容时出错: {str(e)}")

            try:
                # 添加说文解字内容
                self._add_swjz_content(content_div, char_data)
            except Exception as e:
                logger.error(f"添加说文解字内容时出错: {str(e)}")

            try:
                # 添加例句
                self._add_examples(content_div, char_data)
            except Exception as e:
                logger.error(f"添加例句时出错: {str(e)}")

            try:
                # 添加常用词组
                self._add_word_phrases(content_div, char_data)
            except Exception as e:
                logger.error(f"添加常用词组时出错: {str(e)}")

            try:
                # 添加图片
                self._add_images(content_div, char_data)
            except Exception as e:
                logger.error(f"添加图片时出错: {str(e)}")

            try:
                # 添加音频
                self._add_audio(content_div, char_data)
            except Exception as e:
                logger.error(f"添加音频时出错: {str(e)}")

            try:
                # 添加额外信息
                self._add_additional_info(content_div, char_data)
            except Exception as e:
                logger.error(f"添加额外信息时出错: {str(e)}")

            # 生成XML字符串
            try:
                xml_str = etree.tostring(
                    entry,
                    encoding=XML_ENCODING,
                    xml_declaration=False,
                    pretty_print=True,
                ).decode(XML_ENCODING)

                return xml_str
            except Exception as e:
                logger.error(f"生成XML字符串时出错: {str(e)}")
                # 尝试修复可能的XML错误
                try:
                    # 移除可能导致问题的元素
                    for elem in entry.xpath("//*[not(node()) and not(@*)]"):
                        elem.getparent().remove(elem)

                    # 再次尝试生成XML
                    xml_str = etree.tostring(
                        entry,
                        encoding=XML_ENCODING,
                        xml_declaration=False,
                        pretty_print=True,
                    ).decode(XML_ENCODING)

                    return xml_str
                except Exception as e2:
                    logger.error(f"修复XML后再次生成失败: {str(e2)}")
                    # 创建最小化的XML
                    minimal_entry = etree.Element(f"{{{XML_NAMESPACE}}}entry", nsmap=self.nsmap)
                    minimal_entry.set('id', f"zdic_{character}")
                    minimal_entry.set(f"{{{XML_NAMESPACE}}}title", character)

                    minimal_div = etree.SubElement(minimal_entry, f"{{{XML_NAMESPACE}}}div")
                    minimal_div.set('class', 'entry')

                    h1 = etree.SubElement(minimal_div, "h1")
                    h1.text = character

                    p = etree.SubElement(minimal_div, "p")
                    p.text = "此条目内容转换时出错，请查看原始数据。"

                    # 生成最小化的XML
                    return etree.tostring(
                        minimal_entry,
                        encoding=XML_ENCODING,
                        xml_declaration=False,
                        pretty_print=True,
                    ).decode(XML_ENCODING)

        except Exception as e:
            logger.error(f"转换字符数据为XML时出错: {str(e)}")
            logger.exception(e)

            # 创建最小化的XML
            try:
                character = char_data.get('character', '未知字符')

                minimal_entry = etree.Element(f"{{{XML_NAMESPACE}}}entry", nsmap=self.nsmap)
                minimal_entry.set('id', f"zdic_{character}")
                minimal_entry.set(f"{{{XML_NAMESPACE}}}title", character)

                minimal_div = etree.SubElement(minimal_entry, f"{{{XML_NAMESPACE}}}div")
                minimal_div.set('class', 'entry')

                h1 = etree.SubElement(minimal_div, "h1")
                h1.text = character

                p = etree.SubElement(minimal_div, "p")
                p.text = "此条目内容转换时出错，请查看原始数据。"

                # 生成最小化的XML
                return etree.tostring(
                    minimal_entry,
                    encoding=XML_ENCODING,
                    xml_declaration=False,
                    pretty_print=True,
                ).decode(XML_ENCODING)
            except Exception as e2:
                logger.error(f"创建最小化XML时出错: {str(e2)}")
                raise

    def _add_indexes(self, entry: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加索引

        Args:
            entry: 条目元素
            char_data: 字符数据字典
        """
        character = char_data.get('character', '')

        # 添加主索引
        index = etree.SubElement(entry, f"{{{XML_NAMESPACE}}}index")
        index.set(f"{{{XML_NAMESPACE}}}value", character)
        index.set(f"{{{XML_NAMESPACE}}}title", character)

        # 添加拼音索引
        for pinyin in char_data.get('pinyin', []):
            if pinyin:
                index = etree.SubElement(entry, f"{{{XML_NAMESPACE}}}index")
                index.set(f"{{{XML_NAMESPACE}}}value", pinyin)
                index.set(f"{{{XML_NAMESPACE}}}title", f"{character} ({pinyin})")

                # 添加不带声调的拼音索引
                plain_pinyin = self._remove_tone_marks(pinyin)
                if plain_pinyin != pinyin:
                    index = etree.SubElement(entry, f"{{{XML_NAMESPACE}}}index")
                    index.set(f"{{{XML_NAMESPACE}}}value", plain_pinyin)
                    index.set(f"{{{XML_NAMESPACE}}}title", f"{character} ({pinyin})")

        # 添加注音索引
        for zhuyin in char_data.get('zhuyin', []):
            if zhuyin:
                index = etree.SubElement(entry, f"{{{XML_NAMESPACE}}}index")
                index.set(f"{{{XML_NAMESPACE}}}value", zhuyin)
                index.set(f"{{{XML_NAMESPACE}}}title", f"{character} [{zhuyin}]")
                index.set(f"{{{XML_NAMESPACE}}}yomi", zhuyin)  # 用于日文排序

    def _add_title(self, parent: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加标题

        Args:
            parent: 父元素
            char_data: 字符数据字典
        """
        character = char_data.get('character', '')

        # 创建标题
        h1 = etree.SubElement(parent, "h1")
        h1.text = character

    def _add_pronunciation(self, parent: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加发音

        Args:
            parent: 父元素
            char_data: 字符数据字典
        """
        pinyin = char_data.get('pinyin', [])
        zhuyin = char_data.get('zhuyin', [])

        if not pinyin and not zhuyin:
            return

        # 创建发音div
        pron_div = etree.SubElement(parent, "div")
        pron_div.set('class', 'pronunciation')

        # 添加拼音
        if pinyin:
            pinyin_span = etree.SubElement(pron_div, "span")
            pinyin_span.set('class', 'pinyin')
            pinyin_span.text = ', '.join(pinyin)

            # 添加音频按钮
            for p in pinyin:
                audio_path = f"audio/{p}.mp3"
                if p and os.path.exists(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'output', 'resources', audio_path)):
                    audio = etree.SubElement(pron_div, "span")
                    audio.set('class', 'audio-btn')
                    audio.set('onclick', f"playSound('{audio_path}')")
                    break

        # 添加注音
        if zhuyin:
            if pinyin:  # 如果有拼音，添加分隔符
                sep = etree.SubElement(pron_div, "span")
                sep.text = " | "

            zhuyin_span = etree.SubElement(pron_div, "span")
            zhuyin_span.set('class', 'zhuyin')
            zhuyin_span.text = ', '.join(zhuyin)

    def _add_radical_info(self, parent: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加部首和笔画信息

        Args:
            parent: 父元素
            char_data: 字符数据字典
        """
        radical = char_data.get('radical', '')
        strokes = char_data.get('strokes', 0)

        if not radical and not strokes:
            return

        # 创建部首信息div
        radical_div = etree.SubElement(parent, "div")
        radical_div.set('class', 'radical-info')

        # 添加部首
        if radical:
            radical_span = etree.SubElement(radical_div, "span")
            radical_span.text = f"部首：{radical} "

        # 添加笔画
        if strokes:
            strokes_span = etree.SubElement(radical_div, "span")
            strokes_span.text = f"笔画：{strokes}"

    def _add_basic_definitions(self, parent: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加基本释义

        Args:
            parent: 父元素
            char_data: 字符数据字典
        """
        basic_defs = char_data.get('definitions', {}).get('basic', [])

        if not basic_defs:
            return

        # 创建基本释义div
        basic_div = etree.SubElement(parent, "div")
        basic_div.set('class', 'definition basic')

        # 添加标题
        h2 = etree.SubElement(basic_div, "h2")
        h2.text = "基本释义"

        # 添加释义
        for definition in basic_defs:
            p = etree.SubElement(basic_div, "p")

            # 处理不同类型的定义
            if isinstance(definition, dict):
                # 如果是字典类型，根据类型处理
                def_type = definition.get('type', '')
                content = definition.get('content', '')

                if def_type == 'paragraph':
                    # 处理HTML内容
                    try:
                        # 清理和修复HTML内容
                        cleaned_content = self._clean_html_content(content)
                        html_fragment = f"<div>{cleaned_content}</div>"
                        fragment = etree.fromstring(html_fragment.encode('utf-8'), self.parser)
                        self._append_html_content(p, fragment)
                    except Exception:
                        logger.warning(f"解析HTML内容失败，使用纯文本")
                        # 尝试清理内容并作为纯文本使用
                        try:
                            clean_content = re.sub(r'<[^>]*>', ' ', content)
                            clean_content = re.sub(r'\s+', ' ', clean_content).strip()
                            # 解码HTML实体
                            clean_content = self._decode_html_entities(clean_content)
                            p.text = clean_content or str(content)
                        except:
                            p.text = str(content)

                elif def_type == 'list':
                    # 对于列表类型，创建有序列表
                    items = definition.get('items', [])
                    if items:
                        ol = etree.SubElement(p, "ol")
                        for item in items:
                            li = etree.SubElement(ol, "li")
                            try:
                                li.text = str(item)
                            except Exception as e:
                                logger.warning(f"设置列表项文本时出错: {str(e)}")
                                li.text = "项目内容无法显示"

                elif def_type == 'translations':
                    # 处理翻译内容
                    translations = definition.get('content', {})
                    if translations:
                        dl = etree.SubElement(p, "dl")
                        for lang, trans in translations.items():
                            try:
                                dt = etree.SubElement(dl, "dt")
                                dt.text = lang

                                dd = etree.SubElement(dl, "dd")
                                dd.text = trans
                            except Exception as e:
                                logger.warning(f"添加翻译内容时出错: {str(e)}")

                else:
                    # 默认处理
                    try:
                        p.text = str(content)
                    except Exception as e:
                        logger.warning(f"设置默认内容文本时出错: {str(e)}")
                        p.text = "内容无法显示"
            else:
                # 如果是字符串，直接设置文本
                try:
                    p.text = str(definition)
                except Exception as e:
                    logger.warning(f"设置字符串定义文本时出错: {str(e)}")
                    p.text = "定义内容无法显示"

    def _add_detailed_definitions(self, parent: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加详细释义

        Args:
            parent: 父元素
            char_data: 字符数据字典
        """
        detailed_defs = char_data.get('definitions', {}).get('detailed', [])

        if not detailed_defs:
            return

        # 创建详细释义div
        detailed_div = etree.SubElement(parent, "div")
        detailed_div.set('class', 'definition detailed')

        # 添加标题
        h2 = etree.SubElement(detailed_div, "h2")
        h2.text = "详细释义"

        # 处理不同类型的详细释义内容
        for def_item in detailed_defs:
            item_type = def_item.get('type', '')
            content = def_item.get('content', '')

            if item_type == 'title':
                h3 = etree.SubElement(detailed_div, "h3")
                h3.text = content

            elif item_type == 'heading':
                level = def_item.get('level', 3)
                heading = etree.SubElement(detailed_div, f"h{level}")
                heading.text = content

            elif item_type == 'paragraph':
                p = etree.SubElement(detailed_div, "p")
                # 使用XML解析器处理HTML内容
                try:
                    # 尝试解析HTML内容
                    html_fragment = f"<div>{content}</div>"
                    fragment = etree.fromstring(html_fragment, self.parser)
                    # 将解析后的内容添加到p元素
                    self._append_html_content(p, fragment)
                except Exception as e:
                    # 如果解析失败，直接设置文本内容
                    p.text = content

            elif item_type == 'text':
                span = etree.SubElement(detailed_div, "span")
                span.text = content

            elif item_type == 'word_type':
                div = etree.SubElement(detailed_div, "div")
                div.set('class', 'word-type')
                # 处理HTML内容
                try:
                    html_fragment = f"<div>{content}</div>"
                    fragment = etree.fromstring(html_fragment, self.parser)
                    self._append_html_content(div, fragment)
                except Exception as e:
                    div.text = content

            elif item_type == 'definition':
                div = etree.SubElement(detailed_div, "div")
                div.set('class', 'definition-item')
                # 处理HTML内容
                try:
                    html_fragment = f"<div>{content}</div>"
                    fragment = etree.fromstring(html_fragment, self.parser)
                    self._append_html_content(div, fragment)
                except Exception as e:
                    div.text = content

            elif item_type == 'separator':
                hr = etree.SubElement(detailed_div, "hr")

            elif item_type == 'phrases':
                phrases_div = etree.SubElement(detailed_div, "div")
                phrases_div.set('class', 'phrases')
                # 处理HTML内容
                try:
                    html_fragment = f"<div>{content}</div>"
                    fragment = etree.fromstring(html_fragment, self.parser)
                    self._append_html_content(phrases_div, fragment)
                except Exception as e:
                    phrases_div.text = content

            # 添加对列表类型的支持
            elif item_type == 'list':
                items = def_item.get('items', [])
                if items:
                    ol = etree.SubElement(detailed_div, "ol")
                    for item_content in items:
                        li = etree.SubElement(ol, "li")
                        try:
                            html_fragment = f"<div>{item_content}</div>"
                            fragment = etree.fromstring(html_fragment, self.parser)
                            self._append_html_content(li, fragment)
                        except Exception as e:
                            li.text = item_content

            # 添加对自定义元素的支持
            elif item_type == 'element':
                tag = def_item.get('tag', 'div')
                element = etree.SubElement(detailed_div, tag)
                # 处理HTML内容
                try:
                    html_fragment = f"<div>{content}</div>"
                    fragment = etree.fromstring(html_fragment, self.parser)
                    self._append_html_content(element, fragment)
                except Exception as e:
                    element.text = content

            # 添加对原始内容的支持
            elif item_type == 'raw_content':
                # 尝试解析整个原始内容
                try:
                    html_fragment = f"<div>{content}</div>"
                    fragment = etree.fromstring(html_fragment, self.parser)
                    self._append_html_content(detailed_div, fragment)
                except Exception as e:
                    # 如果解析失败，创建一个div并设置文本内容
                    raw_div = etree.SubElement(detailed_div, "div")
                    raw_div.set('class', 'raw-content')
                    raw_div.text = content

    def _append_html_content(self, parent: etree.Element, html_element: etree.Element) -> None:
        """将HTML内容添加到XML元素

        Args:
            parent: 父XML元素
            html_element: HTML元素
        """
        try:
            # 处理子元素
            for child in html_element:
                try:
                    # 创建新元素
                    new_element = etree.SubElement(parent, child.tag)

                    # 复制属性
                    for name, value in child.attrib.items():
                        try:
                            new_element.set(name, value)
                        except Exception as e:
                            logger.warning(f"设置属性时出错: {name}={value}, {str(e)}")
                            # 尝试修复无效属性
                            try:
                                safe_value = str(value).replace('\x00', '').strip()
                                if safe_value:
                                    new_element.set(name, safe_value)
                            except:
                                pass

                    # 复制文本
                    if child.text:
                        try:
                            new_element.text = child.text
                        except Exception as e:
                            logger.warning(f"设置文本时出错: {str(e)}")
                            # 尝试修复无效文本
                            try:
                                safe_text = str(child.text).replace('\x00', '').strip()
                                if safe_text:
                                    new_element.text = safe_text
                            except:
                                pass

                    # 递归处理子元素
                    self._append_html_content(new_element, child)

                    # 复制尾部文本
                    if child.tail:
                        try:
                            if len(list(parent)) > 0:  # 如果父元素有子元素
                                last_child = parent[-1]  # 获取最后一个子元素
                                if last_child.tail:
                                    last_child.tail += child.tail
                                else:
                                    last_child.tail = child.tail
                            else:  # 如果父元素没有子元素
                                if parent.text:
                                    parent.text += child.tail
                                else:
                                    parent.text = child.tail
                        except Exception as e:
                            logger.warning(f"设置尾部文本时出错: {str(e)}")
                            # 尝试修复无效尾部文本
                            try:
                                safe_tail = str(child.tail).replace('\x00', '').strip()
                                if safe_tail and len(list(parent)) > 0:
                                    last_child = parent[-1]
                                    if last_child.tail:
                                        last_child.tail += safe_tail
                                    else:
                                        last_child.tail = safe_tail
                            except:
                                pass
                except Exception as e:
                    logger.error(f"处理HTML子元素时出错: {str(e)}")
                    # 如果处理子元素失败，尝试添加文本内容
                    try:
                        if child.text and child.text.strip():
                            span = etree.SubElement(parent, "span")
                            span.text = child.text.strip()
                    except:
                        pass
        except Exception as e:
            logger.error(f"将HTML内容添加到XML元素时出错: {str(e)}")

    def _add_examples(self, parent: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加例句

        Args:
            parent: 父元素
            char_data: 字符数据字典
        """
        examples = char_data.get('definitions', {}).get('examples', [])

        if not examples:
            return

        # 创建例句div
        examples_div = etree.SubElement(parent, "div")
        examples_div.set('class', 'examples')

        # 添加标题
        h2 = etree.SubElement(examples_div, "h2")
        h2.text = "例句"

        # 添加例句
        ul = etree.SubElement(examples_div, "ul")

        for example in examples:
            li = etree.SubElement(ul, "li")
            li.set('class', 'example')

            # 处理例句内容
            if isinstance(example, dict):
                content = example.get('content', '')
                source = example.get('source', '')

                if content:
                    content_span = etree.SubElement(li, "span")
                    content_span.set('class', 'example-content')
                    try:
                        html_fragment = f"<div>{content}</div>"
                        fragment = etree.fromstring(html_fragment, self.parser)
                        self._append_html_content(content_span, fragment)
                    except Exception as e:
                        content_span.text = content

                if source:
                    # 添加来源
                    if content:  # 如果有内容，添加分隔符
                        sep = etree.SubElement(li, "span")
                        sep.text = " —— "

                    source_span = etree.SubElement(li, "span")
                    source_span.set('class', 'example-source')
                    try:
                        html_fragment = f"<div>{source}</div>"
                        fragment = etree.fromstring(html_fragment, self.parser)
                        self._append_html_content(source_span, fragment)
                    except Exception as e:
                        source_span.text = source
            else:
                # 如果是字符串，直接添加
                li.text = str(example)

    def _add_images(self, parent: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加图片

        Args:
            parent: 父元素
            char_data: 字符数据字典
        """
        images = char_data.get('resources', {}).get('images', [])

        if not images:
            return

        # 创建图片div
        images_div = etree.SubElement(parent, "div")
        images_div.set('class', 'images')

        # 添加标题
        h2 = etree.SubElement(images_div, "h2")
        h2.text = "图片"

        # 添加图片
        for image in images:
            if not image.get('src'):
                continue

            figure = etree.SubElement(images_div, "figure")

            img = etree.SubElement(figure, "img")
            img.set('src', image['src'])
            img.set('alt', char_data.get('character', ''))

    def _add_audio(self, parent: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加音频

        Args:
            parent: 父元素
            char_data: 字符数据字典
        """
        audio_files = char_data.get('resources', {}).get('audio', [])

        if not audio_files:
            return

        # 创建音频div
        audio_div = etree.SubElement(parent, "div")
        audio_div.set('class', 'audio')
        audio_div.set('style', 'display:none;')  # 隐藏音频元素

        # 添加音频
        for audio_file in audio_files:
            if not audio_file.get('src'):
                continue

            audio = etree.SubElement(audio_div, "audio")
            audio.set('src', audio_file['src'])
            audio.set('preload', 'none')

    def _remove_tone_marks(self, pinyin: str) -> str:
        """移除拼音中的声调

        Args:
            pinyin: 带声调的拼音

        Returns:
            不带声调的拼音
        """
        # 声调映射
        tone_marks = {
            'ā': 'a', 'á': 'a', 'ǎ': 'a', 'à': 'a',
            'ē': 'e', 'é': 'e', 'ě': 'e', 'è': 'e',
            'ī': 'i', 'í': 'i', 'ǐ': 'i', 'ì': 'i',
            'ō': 'o', 'ó': 'o', 'ǒ': 'o', 'ò': 'o',
            'ū': 'u', 'ú': 'u', 'ǔ': 'u', 'ù': 'u',
            'ǖ': 'ü', 'ǘ': 'ü', 'ǚ': 'ü', 'ǜ': 'ü',
        }

        # 替换声调
        result = ''
        for char in pinyin:
            result += tone_marks.get(char, char)

        return result

    def _add_word_phrases(self, parent: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加常用词组

        Args:
            parent: 父元素
            char_data: 字符数据字典
        """
        word_phrases = char_data.get('definitions', {}).get('word_phrases', [])

        if not word_phrases:
            return

        # 创建词组div
        phrases_div = etree.SubElement(parent, "div")
        phrases_div.set('class', 'word-phrases')

        # 添加标题
        h2 = etree.SubElement(phrases_div, "h2")
        h2.text = "常用词组"

        # 添加词组
        ul = etree.SubElement(phrases_div, "ul")

        for phrase in word_phrases:
            li = etree.SubElement(ul, "li")
            li.text = phrase

    def _add_gycd_content(self, parent: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加国语词典内容

        Args:
            parent: 父元素
            char_data: 字符数据字典
        """
        gycd_data = char_data.get('definitions', {}).get('gycd', {})

        if not gycd_data or not gycd_data.get('content'):
            return

        # 创建国语词典div
        gycd_div = etree.SubElement(parent, "div")
        gycd_div.set('class', 'gycd')

        # 添加标题
        h2 = etree.SubElement(gycd_div, "h2")
        h2.text = gycd_data.get('title', '国语词典')

        # 添加内容
        content_div = etree.SubElement(gycd_div, "div")
        content_div.set('class', 'gycd-content')

        content = gycd_data.get('content', '')
        try:
            # 尝试解析HTML内容
            html_fragment = f"<div>{content}</div>"
            fragment = etree.fromstring(html_fragment, self.parser)
            # 将解析后的内容添加到content_div元素
            self._append_html_content(content_div, fragment)
        except Exception as e:
            # 如果解析失败，直接设置文本内容
            content_div.text = content

    def _add_kxzd_content(self, parent: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加康熙字典内容

        Args:
            parent: 父元素
            char_data: 字符数据字典
        """
        kxzd_data = char_data.get('definitions', {}).get('kxzd', {})

        if not kxzd_data or not kxzd_data.get('content'):
            return

        # 创建康熙字典div
        kxzd_div = etree.SubElement(parent, "div")
        kxzd_div.set('class', 'kxzd')

        # 添加标题
        h2 = etree.SubElement(kxzd_div, "h2")
        h2.text = kxzd_data.get('title', '康熙字典')

        # 添加图片
        if kxzd_data.get('image'):
            figure = etree.SubElement(kxzd_div, "figure")
            figure.set('class', 'kxzd-image')

            img = etree.SubElement(figure, "img")
            img.set('src', kxzd_data['image'])
            img.set('alt', f"{char_data.get('character', '')}的康熙字典图片")

        # 添加内容
        content_div = etree.SubElement(kxzd_div, "div")
        content_div.set('class', 'kxzd-content')

        content = kxzd_data.get('content', '')
        try:
            # 尝试解析HTML内容
            html_fragment = f"<div>{content}</div>"
            fragment = etree.fromstring(html_fragment, self.parser)
            # 将解析后的内容添加到content_div元素
            self._append_html_content(content_div, fragment)
        except Exception as e:
            # 如果解析失败，直接设置文本内容
            content_div.text = content

    def _add_swjz_content(self, parent: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加说文解字内容

        Args:
            parent: 父元素
            char_data: 字符数据字典
        """
        swjz_data = char_data.get('definitions', {}).get('swjz', {})

        if not swjz_data or not swjz_data.get('content'):
            return

        # 创建说文解字div
        swjz_div = etree.SubElement(parent, "div")
        swjz_div.set('class', 'swjz')

        # 添加标题
        h2 = etree.SubElement(swjz_div, "h2")
        h2.text = swjz_data.get('title', '说文解字')

        # 添加图片
        if swjz_data.get('image'):
            figure = etree.SubElement(swjz_div, "figure")
            figure.set('class', 'swjz-image')

            img = etree.SubElement(figure, "img")
            img.set('src', swjz_data['image'])
            img.set('alt', f"{char_data.get('character', '')}的说文解字图片")

        # 添加内容
        content_div = etree.SubElement(swjz_div, "div")
        content_div.set('class', 'swjz-content')

        content = swjz_data.get('content', '')
        try:
            # 尝试解析HTML内容
            html_fragment = f"<div>{content}</div>"
            fragment = etree.fromstring(html_fragment, self.parser)
            # 将解析后的内容添加到content_div元素
            self._append_html_content(content_div, fragment)
        except Exception as e:
            # 如果解析失败，直接设置文本内容
            content_div.text = content

    def _add_additional_info(self, parent: etree.Element, char_data: Dict[str, Any]) -> None:
        """添加额外信息

        Args:
            parent: 父元素
            char_data: 字符数据字典
        """
        additional_info = char_data.get('additional_info', {})

        if not additional_info:
            return

        # 创建额外信息div
        info_div = etree.SubElement(parent, "div")
        info_div.set('class', 'additional-info')

        # 添加标题
        h2 = etree.SubElement(info_div, "h2")
        h2.text = "额外信息"

        # 添加表格
        table = etree.SubElement(info_div, "table")
        table.set('class', 'info-table')

        # 添加表头
        thead = etree.SubElement(table, "thead")
        tr = etree.SubElement(thead, "tr")

        th1 = etree.SubElement(tr, "th")
        th1.text = "属性"

        th2 = etree.SubElement(tr, "th")
        th2.text = "值"

        # 添加表体
        tbody = etree.SubElement(table, "tbody")

        for key, value in additional_info.items():
            tr = etree.SubElement(tbody, "tr")

            td1 = etree.SubElement(tr, "td")
            td1.text = key

            td2 = etree.SubElement(tr, "td")
            td2.text = str(value)

# 导出函数
def convert_to_xml(char_data: Dict[str, Any]) -> str:
    """将字符数据转换为XML格式

    Args:
        char_data: 字符数据字典

    Returns:
        XML字符串
    """
    converter = Converter()
    return converter.convert_to_xml(char_data)