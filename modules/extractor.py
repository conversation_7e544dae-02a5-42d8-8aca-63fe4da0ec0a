#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
提取模块
从汉典HTML文件中提取字符信息
"""

import os
import re
import logging
import html
from typing import Dict, List, Any, Optional, Tuple
from bs4 import BeautifulSoup, Tag, NavigableString

from modules.config import setup_logging
from modules.resource_manager import ResourceManager

# 获取logger
logger = logging.getLogger('zdic_converter')

class Extractor:
    """HTML提取器类"""
    
    def __init__(self, resource_manager: Optional[ResourceManager] = None):
        """初始化提取器
        
        Args:
            resource_manager: 资源管理器实例，如果为None则创建新实例
        """
        self.resource_manager = resource_manager or ResourceManager()
    
    def extract_from_html(self, html_path: str) -> Dict[str, Any]:
        """从HTML文件中提取字符信息
        
        Args:
            html_path: HTML文件路径
            
        Returns:
            包含字符信息的字典
        """
        try:
            # 读取HTML文件
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 预处理HTML内容，修复常见问题
            html_content = self._preprocess_html(html_content)
            
            # 解析HTML
            soup = BeautifulSoup(html_content, 'lxml')
            
            # 获取字符
            character = self._extract_character(soup, html_path)
            
            # 初始化结果字典
            result = {
                'character': character,
                'pinyin': [],
                'zhuyin': [],
                'radical': '',
                'strokes': 0,
                'additional_info': {},
                'definitions': {
                    'basic': [],
                    'detailed': [],
                    'gycd': {},
                    'kxzd': {},
                    'swjz': {},
                    'examples': [],
                    'word_phrases': []
                },
                'resources': {
                    'images': [],
                    'audio': []
                }
            }
            
            # 使用try-except包装每个提取步骤，确保单个步骤失败不会影响整体提取
            try:
                # 获取拼音和注音
                pinyin, zhuyin = self._extract_pronunciation(soup)
                result['pinyin'] = pinyin
                result['zhuyin'] = zhuyin
            except Exception as e:
                logger.error(f"提取拼音和注音时出错: {str(e)}")
            
            try:
                # 获取部首和笔画
                radical, strokes, additional_info = self._extract_radical_info(soup)
                result['radical'] = radical
                result['strokes'] = strokes
                result['additional_info'] = additional_info
            except Exception as e:
                logger.error(f"提取部首和笔画信息时出错: {str(e)}")
            
            try:
                # 获取基本释义
                basic_definitions = self._extract_basic_definitions(soup)
                result['definitions']['basic'] = basic_definitions
            except Exception as e:
                logger.error(f"提取基本释义时出错: {str(e)}")
            
            try:
                # 获取详细释义
                detailed_definitions = self._extract_detailed_definitions(soup)
                result['definitions']['detailed'] = detailed_definitions
            except Exception as e:
                logger.error(f"提取详细释义时出错: {str(e)}")
            
            try:
                # 获取国语词典内容
                gycd_content = self._extract_gycd_content(soup)
                result['definitions']['gycd'] = gycd_content
            except Exception as e:
                logger.error(f"提取国语词典内容时出错: {str(e)}")
            
            try:
                # 获取康熙字典内容
                kxzd_content = self._extract_kxzd_content(soup)
                result['definitions']['kxzd'] = kxzd_content
            except Exception as e:
                logger.error(f"提取康熙字典内容时出错: {str(e)}")
            
            try:
                # 获取说文解字内容
                swjz_content = self._extract_swjz_content(soup)
                result['definitions']['swjz'] = swjz_content
            except Exception as e:
                logger.error(f"提取说文解字内容时出错: {str(e)}")
            
            try:
                # 获取例句
                examples = self._extract_examples(soup)
                result['definitions']['examples'] = examples
            except Exception as e:
                logger.error(f"提取例句时出错: {str(e)}")
            
            try:
                # 获取常用词组
                word_phrases = self._extract_word_phrases(soup)
                result['definitions']['word_phrases'] = word_phrases
            except Exception as e:
                logger.error(f"提取常用词组时出错: {str(e)}")
            
            try:
                # 获取图片
                images = self._extract_images(soup, html_path)
                result['resources']['images'] = images
            except Exception as e:
                logger.error(f"提取图片时出错: {str(e)}")
            
            try:
                # 获取音频
                audio = self._extract_audio(soup, html_path)
                result['resources']['audio'] = audio
            except Exception as e:
                logger.error(f"提取音频时出错: {str(e)}")
            
            logger.debug(f"成功从{html_path}提取字符信息: {character}")
            return result
            
        except Exception as e:
            logger.error(f"从{html_path}提取字符信息时出错: {str(e)}")
            logger.exception(e)
            
            # 尝试提取字符，即使其他步骤失败
            try:
                character = os.path.basename(html_path).split('_')[0]
                return {
                    'character': character,
                    'pinyin': [],
                    'zhuyin': [],
                    'radical': '',
                    'strokes': 0,
                    'additional_info': {},
                    'definitions': {
                        'basic': [{'type': 'paragraph', 'content': '提取内容时出错，请查看原始文件。'}],
                        'detailed': [],
                        'gycd': {},
                        'kxzd': {},
                        'swjz': {},
                        'examples': [],
                        'word_phrases': []
                    },
                    'resources': {
                        'images': [],
                        'audio': []
                    }
                }
            except:
                raise
    
    def _preprocess_html(self, html_content: str) -> str:
        """预处理HTML内容，修复常见问题
        
        Args:
            html_content: 原始HTML内容
            
        Returns:
            处理后的HTML内容
        """
        # 修复未闭合的标签
        html_content = re.sub(r'<(br|hr|img|input|meta|link)(?![^>]*/>)([^>]*)>', r'<\1\2 />', html_content)
        
        # 特别处理img标签嵌套在strong标签中的情况
        html_content = re.sub(r'<img([^>]*)>(</strong>)', r'<img\1/>\2', html_content)
        
        # 修复self-closing标签
        html_content = re.sub(r'<([a-z]+)([^>]*)></\1>', r'<\1\2/>', html_content)
        
        # 修复无效的HTML实体
        html_content = re.sub(r'&(?![a-zA-Z#][a-zA-Z0-9]{1,8};)', '&amp;', html_content)
        
        # 移除控制字符
        html_content = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', html_content)
        
        # 修复嵌套错误的标签
        html_content = re.sub(r'(<[a-zA-Z]+)([^>]*>)(.*?)(<\1)', r'\1\2\3</\1>\4', html_content)
        
        # 修复未闭合的注释标签
        html_content = re.sub(r'<!--((?!-->).)*$', '', html_content, flags=re.DOTALL)
        
        # 修复未闭合的CDATA标签
        html_content = re.sub(r'<!\[CDATA\[((?!\]\]>).)*$', '', html_content, flags=re.DOTALL)
        
        # 移除HTML注释（可选，如果注释内容导致解析问题）
        # html_content = re.sub(r'<!--.*?-->', '', html_content, flags=re.DOTALL)
        
        # 修复包含换行符的标签
        html_content = re.sub(r'<([a-zA-Z]+)[^>]*\n[^>]*>', r'<\1>', html_content)
        
        # 修复错误嵌套的引号
        html_content = re.sub(r'(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")([^"]*?)(")', r'\1\2\3\4\5\6\7\8\9\10\11\12\13\14\15\16\17\18\19\20\21\22\23\24\25\26\27\28\29\30\31\32\33\34\35\36\37\38\39', html_content)
        
        return html_content
    
    def _extract_character(self, soup: BeautifulSoup, html_path: str) -> str:
        """提取字符
        
        Args:
            soup: BeautifulSoup对象
            html_path: HTML文件路径
            
        Returns:
            字符
        """
        # 尝试从标题提取
        title_tag = soup.find('h1')
        if title_tag and title_tag.text.strip():
            return title_tag.text.strip()
        
        # 尝试从文件名提取
        filename = os.path.basename(html_path)
        match = re.match(r'(.+)_original\.html', filename)
        if match:
            return match.group(1)
        
        # 如果都失败，抛出异常
        raise ValueError(f"无法从{html_path}提取字符")
    
    def _extract_pronunciation(self, soup: BeautifulSoup) -> Tuple[List[str], List[str]]:
        """提取拼音和注音
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            拼音列表和注音列表
        """
        pinyin = []
        zhuyin = []
        
        # 查找拼音
        pinyin_tags = soup.find_all(class_='dicpy')
        for tag in pinyin_tags:
            text = tag.text.strip()
            if text:
                # 分割多音字
                for py in re.split(r'[,，、\s]+', text):
                    py = re.sub(r'<.*?>', '', py).strip()
                    if py and py not in pinyin:
                        pinyin.append(py)
        
        # 查找注音
        zhuyin_tags = soup.find_all(class_='z_zy')
        for tag in zhuyin_tags:
            text = tag.text.strip()
            if text:
                # 分割多音字
                for zy in re.split(r'[,，、\s]+', text):
                    zy = re.sub(r'<.*?>', '', zy).strip()
                    if zy and zy not in zhuyin:
                        zhuyin.append(zy)
        
        return pinyin, zhuyin
    
    def _extract_radical_info(self, soup: BeautifulSoup) -> Tuple[str, int, Dict[str, str]]:
        """提取部首和笔画信息
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            部首、笔画数和额外信息
        """
        radical = ""
        strokes = 0
        additional_info = {}
        
        # 查找部首
        radical_tag = soup.find(class_='z_bs2')
        if radical_tag:
            radical_text = radical_tag.text.strip()
            radical_match = re.search(r'部首\s*([^\s]+)', radical_text)
            if radical_match:
                radical = radical_match.group(1)
            
            # 提取部外笔画
            outside_strokes_match = re.search(r'部外\s*(\d+)', radical_text)
            if outside_strokes_match:
                additional_info['outside_strokes'] = outside_strokes_match.group(1)
        
        # 查找总笔画
        strokes_pattern = re.compile(r'总笔画[:：]?\s*(\d+)')
        strokes_tag = soup.find(string=strokes_pattern)
        if strokes_tag:
            match = strokes_pattern.search(strokes_tag)
            if match:
                try:
                    strokes = int(match.group(1))
                except ValueError:
                    pass
        
        # 提取五笔、仓颉、郑码、四角码等信息
        code_table = soup.find('table', class_='dsk')
        if code_table:
            rows = code_table.find_all('tr')
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 2:
                    key = cells[0].text.strip()
                    value = cells[1].text.strip()
                    if key and value:
                        additional_info[key] = value
        
        # 提取统一码
        unicode_tag = soup.find(string=re.compile(r'统一码'))
        if unicode_tag:
            unicode_match = re.search(r'[Uu]\+([0-9A-F]+)', unicode_tag.parent.text)
            if unicode_match:
                additional_info['unicode'] = unicode_match.group(1)
        
        # 提取笔顺
        stroke_order_tag = soup.find(class_='z_bis2')
        if stroke_order_tag:
            stroke_order = stroke_order_tag.text.strip()
            if stroke_order:
                additional_info['stroke_order'] = stroke_order
        
        return radical, strokes, additional_info
    
    def _extract_html_content(self, element) -> str:
        """提取HTML元素的内容，保留格式
        
        Args:
            element: HTML元素
            
        Returns:
            格式化的HTML内容
        """
        if element is None:
            return ""
        
        try:
            if isinstance(element, NavigableString):
                return html.escape(str(element))
            
            if isinstance(element, Tag):
                result = ""
                tag_name = element.name
                
                # 跳过注释节点
                if tag_name == "comment":
                    return ""
                
                # 开始标签
                if tag_name:
                    result += f"<{tag_name}"
                    for attr, value in element.attrs.items():
                        try:
                            if isinstance(value, list):
                                value = " ".join(value)
                            elif isinstance(value, bool) and value:
                                # 处理布尔属性
                                result += f" {attr}"
                                continue
                            
                            # 清理属性值中的无效字符
                            if isinstance(value, str):
                                value = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', value)
                            
                            result += f" {attr}=\"{html.escape(str(value))}\""
                        except Exception as e:
                            logger.warning(f"处理属性时出错: {attr}, {str(e)}")
                    result += ">"
                
                # 内容
                for child in element.children:
                    try:
                        result += self._extract_html_content(child)
                    except Exception as e:
                        logger.warning(f"处理子元素时出错: {str(e)}")
                
                # 结束标签
                if tag_name and tag_name not in ['br', 'hr', 'img', 'input', 'meta', 'link']:
                    result += f"</{tag_name}>"
                
                return result
            
            return ""
        except Exception as e:
            logger.error(f"提取HTML内容时出错: {str(e)}")
            # 返回元素的文本内容作为备选
            try:
                if hasattr(element, 'get_text'):
                    return html.escape(element.get_text())
                elif hasattr(element, 'string'):
                    return html.escape(str(element.string))
                else:
                    return html.escape(str(element))
            except:
                return ""
    
    def _extract_basic_definitions(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """提取基本释义
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            基本释义列表
        """
        definitions = []
        
        # 查找基本释义
        jnr_div = soup.find(class_='jnr')
        if jnr_div:
            # 提取标题
            title = ""
            strong_tag = jnr_div.find('strong')
            if strong_tag:
                title = strong_tag.text.strip()
            
            # 提取所有段落
            for p in jnr_div.find_all('p'):
                if p.text.strip():
                    definitions.append({
                        'type': 'paragraph',
                        'content': self._extract_html_content(p)
                    })
            
            # 提取所有列表
            for ol in jnr_div.find_all('ol'):
                items = []
                for li in ol.find_all('li'):
                    items.append(self._extract_html_content(li))
                
                if items:
                    definitions.append({
                        'type': 'list',
                        'items': items
                    })
            
            # 提取英语、德语、法语等翻译
            enbox = jnr_div.find(class_='enbox')
            if enbox:
                translations = {}
                for p in enbox.find_all('p'):
                    text = p.text.strip()
                    lang_match = re.match(r'([^\s]+)\s+(.+)', text)
                    if lang_match:
                        lang = lang_match.group(1)
                        trans = lang_match.group(2)
                        translations[lang] = trans
                
                if translations:
                    definitions.append({
                        'type': 'translations',
                        'content': translations
                    })
        
        return definitions
    
    def _extract_detailed_definitions(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """提取详细释义
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            详细释义列表
        """
        detailed = []
        
        try:
            # 查找详细释义
            xnr_div = soup.find(class_='xnr')
            if xnr_div:
                # 提取标题
                title = ""
                strong_tag = xnr_div.find('strong')
                if strong_tag:
                    title = strong_tag.text.strip()
                    detailed.append({
                        'type': 'title',
                        'content': title
                    })
                
                # 处理所有子元素
                self._process_definition_elements(xnr_div, detailed)
                
                # 确保提取所有内容
                if not detailed or (len(detailed) == 1 and detailed[0]['type'] == 'title'):
                    # 如果没有提取到详细内容，则提取整个div的内容
                    content = self._extract_html_content(xnr_div)
                    if content.strip():
                        detailed.append({
                            'type': 'raw_content',
                            'content': content
                        })
            
            # 如果仍然没有内容，尝试其他可能的容器
            if not detailed:
                for class_name in ['xnr2', 'detail', 'details', 'content']:
                    div = soup.find(class_=class_name)
                    if div:
                        content = self._extract_html_content(div)
                        if content.strip():
                            detailed.append({
                                'type': 'raw_content',
                                'content': content
                            })
                            break
        except Exception as e:
            logger.error(f"提取详细释义时出错: {str(e)}")
            # 尝试提取任何可能的内容
            try:
                for div in soup.find_all('div'):
                    if div.get('class') and any('content' in c for c in div.get('class')):
                        content = self._extract_html_content(div)
                        if content.strip():
                            detailed.append({
                                'type': 'raw_content',
                                'content': content
                            })
                            break
            except:
                pass
        
        return detailed
    
    def _process_definition_elements(self, parent: Tag, result: List[Dict[str, Any]]) -> None:
        """处理定义元素
        
        Args:
            parent: 父元素
            result: 结果列表
        """
        # 处理所有直接子元素
        for child in parent.children:
            if isinstance(child, NavigableString):
                # 如果是文本节点且不为空，添加为段落
                text = child.strip()
                if text:
                    result.append({
                        'type': 'text',
                        'content': text
                    })
            elif isinstance(child, Tag):
                if child.name == 'p':
                    # 处理段落
                    content = self._extract_html_content(child)
                    if content.strip():
                        # 检查是否包含词性标记
                        if re.search(r'〈[^〉]+〉', child.text):
                            result.append({
                                'type': 'word_type',
                                'content': content
                            })
                        else:
                            result.append({
                                'type': 'paragraph',
                                'content': content
                            })
                
                elif child.name == 'hr':
                    # 处理分隔线
                    result.append({
                        'type': 'separator'
                    })
                
                elif child.name == 'div':
                    # 处理div
                    if 'cit' in child.get('class', []):
                        # 常用词组
                        result.append({
                            'type': 'phrases',
                            'content': self._extract_html_content(child)
                        })
                    else:
                        # 其他div，递归处理
                        self._process_definition_elements(child, result)
                
                elif child.name == 'ol' or child.name == 'ul':
                    # 处理列表
                    items = []
                    for li in child.find_all('li', recursive=False):
                        items.append(self._extract_html_content(li))
                    
                    if items:
                        result.append({
                            'type': 'list',
                            'items': items
                        })
                
                elif child.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                    # 处理标题
                    result.append({
                        'type': 'heading',
                        'level': int(child.name[1]),
                        'content': child.text.strip()
                    })
                
                else:
                    # 其他元素，提取内容
                    content = self._extract_html_content(child)
                    if content.strip():
                        result.append({
                            'type': 'element',
                            'tag': child.name,
                            'content': content
                        })
    
    def _extract_gycd_content(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """提取国语词典内容
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            国语词典内容
        """
        result = {
            'title': '',
            'content': []
        }
        
        # 查找国语词典内容
        gycd_div = soup.find(class_='gycd')
        if gycd_div:
            # 提取标题
            title_tag = soup.find(class_='h2_entry', string=lambda s: '国语' in s if s else False)
            if title_tag:
                result['title'] = title_tag.text.strip()
            
            # 提取全部内容
            result['content'] = self._extract_html_content(gycd_div)
        
        return result
    
    def _extract_kxzd_content(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """提取康熙字典内容
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            康熙字典内容
        """
        result = {
            'title': '',
            'content': []
        }
        
        # 查找康熙字典内容
        kxzd_div = soup.find(class_='knr')
        if kxzd_div:
            # 提取标题
            title_tag = soup.find(class_='h2_entry', string=lambda s: '康熙' in s if s else False)
            if title_tag:
                result['title'] = title_tag.text.strip()
            
            # 提取图片
            img_tag = kxzd_div.find('img', class_='kxtimg')
            if img_tag:
                src = img_tag.get('src') or img_tag.get('data-original-src')
                if src:
                    result['image'] = src
            
            # 提取全部内容
            result['content'] = self._extract_html_content(kxzd_div)
        
        return result
    
    def _extract_swjz_content(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """提取说文解字内容
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            说文解字内容
        """
        result = {
            'title': '',
            'content': []
        }
        
        # 查找说文解字内容
        swjz_div = soup.find(class_='snr')
        if swjz_div:
            # 提取标题
            title_tag = soup.find(class_='h2_entry', string=lambda s: '说文' in s if s else False)
            if title_tag:
                result['title'] = title_tag.text.strip()
            
            # 提取图片
            img_tag = swjz_div.find('img', class_='kxtimg')
            if img_tag:
                src = img_tag.get('src') or img_tag.get('data-original-src')
                if src:
                    result['image'] = src
            
            # 提取全部内容
            result['content'] = self._extract_html_content(swjz_div)
        
        return result
    
    def _extract_examples(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """提取例句
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            例句列表
        """
        examples = []
        
        # 查找例句
        example_tags = soup.find_all(class_='diczx1')
        for tag in example_tags:
            content = self._extract_html_content(tag)
            if content.strip():
                examples.append({
                    'content': content
                })
        
        # 查找引用
        quote_tags = soup.find_all(class_='smcs')
        for tag in quote_tags:
            content = self._extract_html_content(tag)
            if content.strip():
                examples.append({
                    'source': content
                })
        
        return examples
    
    def _extract_word_phrases(self, soup: BeautifulSoup) -> List[str]:
        """提取常用词组
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            常用词组列表
        """
        phrases = []
        
        # 查找常用词组
        cit_div = soup.find(class_='cit')
        if cit_div:
            for a_tag in cit_div.find_all('a'):
                phrase = a_tag.text.strip()
                if phrase and phrase not in phrases:
                    phrases.append(phrase)
        
        return phrases
    
    def _extract_images(self, soup: BeautifulSoup, html_path: str) -> List[Dict[str, str]]:
        """提取图片
        
        Args:
            soup: BeautifulSoup对象
            html_path: HTML文件路径
            
        Returns:
            图片信息列表
        """
        images = []
        
        # 查找所有图片标签
        img_tags = soup.find_all('img')
        for img in img_tags:
            src = img.get('src') or img.get('data-original-src')
            if not src:
                continue
            
            # 处理图片路径
            if src.startswith(('http://', 'https://')):
                # 远程图片
                image_info = self.resource_manager.process_remote_image(src)
            else:
                # 本地图片
                base_dir = os.path.dirname(html_path)
                image_path = os.path.join(base_dir, src)
                if not os.path.exists(image_path) and src.startswith('../../'):
                    # 尝试修复相对路径
                    src = src[6:]  # 去掉 ../../
                    image_path = os.path.join(os.path.dirname(os.path.dirname(base_dir)), src)
                
                image_info = self.resource_manager.process_local_image(image_path)
            
            if image_info:
                # 添加alt和title属性
                image_info['alt'] = img.get('alt', '')
                image_info['title'] = img.get('title', '')
                images.append(image_info)
        
        return images
    
    def _extract_audio(self, soup: BeautifulSoup, html_path: str) -> List[Dict[str, str]]:
        """提取音频
        
        Args:
            soup: BeautifulSoup对象
            html_path: HTML文件路径
            
        Returns:
            音频信息列表
        """
        audio_files = []
        
        # 查找所有音频标签
        audio_tags = soup.find_all('audio')
        for audio in audio_tags:
            src = audio.get('src') or audio.get('data-src')
            if not src:
                continue
            
            # 处理音频路径
            if src.startswith(('http://', 'https://')):
                # 远程音频
                audio_info = self.resource_manager.process_remote_audio(src)
            else:
                # 本地音频
                base_dir = os.path.dirname(html_path)
                audio_path = os.path.join(base_dir, src)
                if not os.path.exists(audio_path) and src.startswith('../../'):
                    # 尝试修复相对路径
                    src = src[6:]  # 去掉 ../../
                    audio_path = os.path.join(os.path.dirname(os.path.dirname(base_dir)), src)
                
                audio_info = self.resource_manager.process_local_audio(audio_path)
            
            if audio_info:
                # 添加描述
                audio_info['description'] = audio.get('title', '')
                audio_files.append(audio_info)
        
        # 查找音频播放按钮
        audio_btns = soup.find_all(class_='audio_play_button')
        for btn in audio_btns:
            src = btn.get('data-src-mp3')
            if not src:
                continue
            
            # 处理音频路径
            if src.startswith(('http://', 'https://')):
                # 远程音频
                audio_info = self.resource_manager.process_remote_audio(src)
            else:
                # 本地音频
                base_dir = os.path.dirname(html_path)
                audio_path = os.path.join(base_dir, src)
                if not os.path.exists(audio_path) and src.startswith('../../'):
                    # 尝试修复相对路径
                    src = src[6:]  # 去掉 ../../
                    audio_path = os.path.join(os.path.dirname(os.path.dirname(base_dir)), src)
                
                audio_info = self.resource_manager.process_local_audio(audio_path)
            
            if audio_info:
                # 添加描述
                audio_info['description'] = btn.get('title', '')
                audio_files.append(audio_info)
        
        return audio_files

# 导出函数
def extract_from_html(html_path: str) -> Dict[str, Any]:
    """从HTML文件中提取字符信息
    
    Args:
        html_path: HTML文件路径
        
    Returns:
        包含字符信息的字典
    """
    extractor = Extractor()
    return extractor.extract_from_html(html_path) 