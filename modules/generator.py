#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
生成模块
生成最终的macOS词典文件
"""

import os
import glob
import logging
import shutil
from typing import Dict, List, Any, Optional

from modules.config import (
    OUTPUT_DIR,
    OUTPUT_XML_DIR,
    OUTPUT_RESOURCES_DIR,
    DICTIONARY_NAME,
    DICTIONARY_ID,
    DICTIONARY_VERSION,
    DICTIONARY_BUILD
)

# 获取logger
logger = logging.getLogger('zdic_converter')

class Generator:
    """生成器类"""
    
    def __init__(self):
        """初始化生成器"""
        # 确保输出目录存在
        os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    def generate_dictionary_files(self) -> bool:
        """生成词典文件
        
        Returns:
            是否成功生成
        """
        try:
            # 生成词典信息文件
            self._generate_info_plist()
            
            # 生成Makefile
            self._generate_makefile()
            
            # 生成CSS文件
            self._copy_css_file()
            
            # 生成合并后的XML文件
            self._generate_combined_xml()
            
            logger.info("词典文件生成成功")
            return True
            
        except Exception as e:
            logger.error(f"生成词典文件时出错: {str(e)}")
            logger.exception(e)
            return False
    
    def _generate_info_plist(self) -> None:
        """生成Info.plist文件"""
        info_plist_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>zh-Hans</string>
    <key>CFBundleIdentifier</key>
    <string>{DICTIONARY_ID}</string>
    <key>CFBundleName</key>
    <string>{DICTIONARY_NAME}</string>
    <key>CFBundleShortVersionString</key>
    <string>{DICTIONARY_VERSION}</string>
    <key>CFBundleVersion</key>
    <string>{DICTIONARY_BUILD}</string>
    <key>DCSDictionaryCopyright</key>
    <string>汉典数据转换而来，仅供学习研究使用</string>
    <key>DCSDictionaryManufacturerName</key>
    <string>汉典数据转macOS词典转换工具</string>
    <key>DCSDictionaryUseSystemAppearance</key>
    <true/>
    <key>DCSDictionaryLanguages</key>
    <array>
        <dict>
            <key>DCSDictionaryDescriptionKey</key>
            <string>汉典词典</string>
            <key>DCSDictionaryIndexLanguageKey</key>
            <string>zh-Hans</string>
            <key>DCSDictionaryLocaleKey</key>
            <string>zh-Hans</string>
            <key>DCSDictionaryPrimaryLanguageKey</key>
            <string>zh-Hans</string>
        </dict>
    </array>
    <key>DCSDictionaryFrontMatterReferenceID</key>
    <string>front_back_matter</string>
</dict>
</plist>
"""
        
        # 写入Info.plist文件
        with open(os.path.join(OUTPUT_DIR, 'Info.plist'), 'w', encoding='utf-8') as f:
            f.write(info_plist_content)
    
    def _generate_makefile(self) -> None:
        """生成Makefile文件"""
        makefile_content = f"""#
# Makefile for {DICTIONARY_NAME}
#

DICT_NAME       = "{DICTIONARY_NAME}"
DICT_SRC_PATH   = "."
DICT_BUILD_OPTS = -v 1 -s 1 -j 2
DICT_INSTALL_PATH = ~/Library/Dictionaries

XML_FILES = $(wildcard xml/*.xml)
CSS_FILES = resources/styles.css
PLIST_FILE = Info.plist

DICT_DEV_KIT_OBJ_DIR = objects
DICT_DEV_KIT_BIN_DIR = bin

OBJECTS = $(addprefix $(DICT_DEV_KIT_OBJ_DIR)/, $(notdir $(XML_FILES:.xml=.xml)))

all: $(DICT_NAME).dictionary

$(DICT_NAME).dictionary: $(OBJECTS) $(CSS_FILES) $(PLIST_FILE)
	@echo "Building dictionary..."
	@$(DICT_DEV_KIT_BIN_DIR)/build_dict.sh $(DICT_BUILD_OPTS) $(DICT_NAME) $(PLIST_FILE) $(DICT_DEV_KIT_OBJ_DIR) $(CSS_FILES)
	@echo "Done."

$(DICT_DEV_KIT_OBJ_DIR)/%.xml: xml/%.xml
	@echo "Preprocessing $<..."
	@mkdir -p $(DICT_DEV_KIT_OBJ_DIR)
	@$(DICT_DEV_KIT_BIN_DIR)/make_dict.sh $< $@

install: $(DICT_NAME).dictionary
	@echo "Installing dictionary..."
	@mkdir -p $(DICT_INSTALL_PATH)
	@cp -rf $(DICT_NAME).dictionary $(DICT_INSTALL_PATH)
	@echo "Done."
	@echo "To test the new dictionary, try Dictionary.app."

clean:
	@echo "Cleaning..."
	@rm -rf $(DICT_DEV_KIT_OBJ_DIR)
	@rm -f $(DICT_NAME).dictionary
	@echo "Done."
"""
        
        # 写入Makefile文件
        with open(os.path.join(OUTPUT_DIR, 'Makefile'), 'w', encoding='utf-8') as f:
            f.write(makefile_content)
    
    def _copy_css_file(self) -> None:
        """复制CSS文件"""
        # 创建CSS文件
        css_dir = os.path.join(OUTPUT_DIR, 'resources')
        os.makedirs(css_dir, exist_ok=True)
        
        css_content = """
/* 汉典词典样式表 */
body {
    font-family: -apple-system, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
    line-height: 1.5;
    margin: 0;
    padding: 10px;
}

h1 {
    font-size: 24px;
    font-weight: bold;
    margin: 0 0 10px 0;
    color: #333;
}

h2 {
    font-size: 18px;
    font-weight: bold;
    margin: 15px 0 10px 0;
    color: #444;
}

.entry {
    margin: 0;
    padding: 0;
}

.pronunciation {
    margin: 10px 0;
    color: #666;
}

.pinyin {
    color: #0066cc;
    font-weight: bold;
}

.zhuyin {
    color: #6600cc;
}

.radical-info {
    margin: 10px 0;
    color: #666;
}

.definitions {
    margin: 15px 0;
}

.basic-definitions {
    margin-bottom: 10px;
}

.detailed-definitions {
    margin-bottom: 10px;
}

.definition-item {
    margin: 5px 0;
    padding-left: 20px;
}

.examples {
    margin: 15px 0;
    color: #444;
}

.example-item {
    margin: 5px 0;
    padding-left: 20px;
    font-style: italic;
}

.images {
    margin: 15px 0;
    text-align: center;
}

.image-item {
    margin: 10px 0;
}

.audio-btn {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url('speaker.png');
    background-repeat: no-repeat;
    background-size: contain;
    cursor: pointer;
    vertical-align: middle;
    margin-left: 5px;
}

.about {
    margin: 20px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
}

@media (prefers-color-scheme: dark) {
    body {
        background-color: #222;
        color: #ddd;
    }
    
    h1 {
        color: #eee;
    }
    
    h2 {
        color: #ccc;
    }
    
    .pinyin {
        color: #66aaff;
    }
    
    .zhuyin {
        color: #aa88ff;
    }
    
    .about {
        background-color: #333;
        border-color: #555;
    }
}
"""
        
        # 写入CSS文件
        with open(os.path.join(css_dir, 'styles.css'), 'w', encoding='utf-8') as f:
            f.write(css_content)
    
    def _generate_front_matter(self) -> None:
        """生成前言文件"""
        front_matter_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<d:entry id="front_back_matter" d:title="前言" xmlns:d="http://www.apple.com/DTDs/DictionaryService-1.0.rng">
    <d:index d:value="前言"/>
    <d:index d:value="说明"/>
    <d:index d:value="关于"/>
    <div class="about">
        <h1>汉典词典</h1>
        <p>本词典由汉典数据转macOS词典转换工具生成，仅供学习研究使用。</p>
        <p>版本：{DICTIONARY_VERSION}</p>
        <p>构建：{DICTIONARY_BUILD}</p>
        <p>数据来源：汉典网</p>
    </div>
</d:entry>
"""
        
        # 写入前言文件
        front_matter_path = os.path.join(OUTPUT_XML_DIR, 'front_matter.xml')
        with open(front_matter_path, 'w', encoding='utf-8') as f:
            f.write(front_matter_content)
    
    def _generate_example_xml(self) -> None:
        """生成示例XML文件"""
        example_content = """<?xml version="1.0" encoding="UTF-8"?>
<d:entry id="zdic_示例" d:title="示例" xmlns:d="http://www.apple.com/DTDs/DictionaryService-1.0.rng">
    <d:index d:value="示例"/>
    <d:index d:value="样例"/>
    <div class="entry">
        <h1>示例</h1>
        <div class="pronunciation">
            <span class="pinyin">shì lì</span>
        </div>
        <div class="definitions">
            <div class="basic-definitions">
                <p>作为例子来说明的事物。</p>
            </div>
        </div>
    </div>
</d:entry>
"""
        
        # 写入示例文件
        example_path = os.path.join(OUTPUT_XML_DIR, 'example.xml')
        with open(example_path, 'w', encoding='utf-8') as f:
            f.write(example_content)
    
    def _generate_combined_xml(self) -> None:
        """生成合并后的XML文件"""
        # 创建对象目录
        obj_dir = os.path.join(OUTPUT_DIR, 'objects')
        os.makedirs(obj_dir, exist_ok=True)
        
        # 生成前言XML
        self._generate_front_matter()
        
        # 查找所有XML文件
        xml_files = glob.glob(os.path.join(OUTPUT_XML_DIR, '*.xml'))
        
        # 准备条目XML内容列表
        entries = []
        
        # 首先添加前言
        front_matter_path = os.path.join(OUTPUT_XML_DIR, 'front_matter.xml')
        if os.path.exists(front_matter_path):
            try:
                with open(front_matter_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 移除XML声明和DOCTYPE
                    content = self._remove_xml_declaration(content)
                    entries.append(content)
            except Exception as e:
                logger.error(f"读取前言XML文件时出错: {str(e)}")
        
        # 添加所有字符条目
        count = 0
        for xml_file in xml_files:
            # 跳过前言
            if os.path.basename(xml_file) == 'front_matter.xml':
                continue
                
            try:
                with open(xml_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 移除XML声明和DOCTYPE
                    content = self._remove_xml_declaration(content)
                    entries.append(content)
                    count += 1
            except Exception as e:
                logger.error(f"读取XML文件时出错: {xml_file}, 错误: {str(e)}")
        
        logger.info(f"已复制 {count} 个XML文件到输出目录")
        
        # 使用Converter创建词典XML
        try:
            from modules.converter import Converter
            converter = Converter()
            combined_path = os.path.join(obj_dir, 'combined.xml')
            if converter.create_dictionary_xml(entries, combined_path):
                logger.info(f"合并的XML文件已生成: {combined_path}")
            else:
                logger.error("生成合并的XML文件失败")
        except Exception as e:
            logger.error(f"使用Converter创建词典XML时出错: {str(e)}")
            
            # 回退到原始方法
            self._fallback_generate_combined_xml(entries, obj_dir)
    
    def _fallback_generate_combined_xml(self, entries: List[str], obj_dir: str) -> None:
        """回退方法：使用简单字符串拼接生成合并的XML文件
        
        Args:
            entries: XML条目列表
            obj_dir: 输出目录
        """
        try:
            # 合并XML内容
            xml_header = f'''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE d:dictionary SYSTEM "file://localhost/System/Library/DTDs/DictionaryService-1.0.rng">
<d:dictionary xmlns="http://www.w3.org/1999/xhtml" xmlns:d="http://www.apple.com/DTDs/DictionaryService-1.0.rng">
'''
            
            xml_footer = '''
</d:dictionary>
'''
            
            # 写入合并后的XML文件
            combined_path = os.path.join(obj_dir, 'combined.xml')
            with open(combined_path, 'w', encoding='utf-8') as f:
                f.write(xml_header)
                for entry in entries:
                    f.write(entry)
                    f.write('\n')
                f.write(xml_footer)
            
            logger.info(f"合并的XML文件已生成(回退方法): {combined_path}")
        except Exception as e:
            logger.error(f"使用回退方法生成合并的XML文件时出错: {str(e)}")
            logger.exception(e)
    
    def _remove_xml_declaration(self, xml_content: str) -> str:
        """移除XML声明和DOCTYPE
        
        Args:
            xml_content: XML内容字符串
            
        Returns:
            处理后的XML内容
        """
        # 移除XML声明
        xml_content = xml_content.replace('<?xml version="1.0" encoding="UTF-8"?>', '')
        # 移除DOCTYPE
        xml_content = xml_content.replace('<!DOCTYPE d:dictionary SYSTEM "file://localhost/System/Library/DTDs/DictionaryService-1.0.rng">', '')
        # 移除前导空白
        xml_content = xml_content.lstrip()
        return xml_content

# 导出函数
def generate_dictionary_files() -> bool:
    """生成词典文件
    
    Returns:
        是否成功生成
    """
    generator = Generator()
    return generator.generate_dictionary_files()

def generate_all_dictionary_files(version: str = None) -> bool:
    """生成所有词典文件
    
    Args:
        version: 词典版本号，如果为None则使用默认值
        
    Returns:
        是否成功生成
    """
    # 更新版本号
    global DICTIONARY_VERSION
    if version:
        DICTIONARY_VERSION = version
    
    # 生成词典文件
    return generate_dictionary_files()

def generate_front_matter() -> bool:
    """生成前言文件
    
    Returns:
        是否成功生成
    """
    try:
        generator = Generator()
        generator._generate_front_matter()
        return True
    except Exception as e:
        logger.error(f"生成前言文件时出错: {str(e)}")
        logger.exception(e)
        return False 