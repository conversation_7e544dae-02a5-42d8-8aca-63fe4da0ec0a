#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
资源管理模块
处理图片、音频等资源文件
"""

import os
import re
import shutil
import hashlib
import logging
import threading
import urllib.request
from typing import Dict, Any, Optional, List, Set
from functools import lru_cache
from urllib.parse import urlparse

from modules.config import (
    OUTPUT_IMAGES_DIR, 
    OUTPUT_AUDIO_DIR, 
    CACHE_SIZE,
    MAX_IMAGE_WIDTH,
    MAX_IMAGE_HEIGHT,
    AUDIO_FORMAT,
    AUDIO_BITRATE
)

# 获取logger
logger = logging.getLogger('zdic_converter')

class ResourceManager:
    """资源管理器类"""
    
    def __init__(self):
        """初始化资源管理器"""
        # 确保输出目录存在
        os.makedirs(OUTPUT_IMAGES_DIR, exist_ok=True)
        os.makedirs(OUTPUT_AUDIO_DIR, exist_ok=True)
        
        # 资源缓存
        self._image_cache = {}
        self._audio_cache = {}
        
        # 内容哈希缓存，用于去重
        self._image_hash_cache = {}  # 哈希值 -> 文件路径
        self._audio_hash_cache = {}  # 哈希值 -> 文件路径
        
        # 已处理的资源集合
        self._processed_images = set()
        self._processed_audio = set()
        
        # 线程锁，防止并发操作缓存
        self._image_lock = threading.RLock()
        self._audio_lock = threading.RLock()
    
    def process_resources(self, char_data: Dict[str, Any], output_dir: str, resource_types: List[str] = None) -> bool:
        """处理字符数据中的所有资源
        
        Args:
            char_data: 字符数据字典
            output_dir: 输出目录
            resource_types: 要处理的资源类型列表，默认为['images', 'audio']
            
        Returns:
            是否成功处理所有资源
        """
        try:
            # 默认处理所有资源类型
            if resource_types is None:
                resource_types = ['images', 'audio']
            
            # 处理图片资源
            if 'images' in resource_types:
                images = char_data.get('resources', {}).get('images', [])
                processed_images = []
                
                for image in images:
                    if 'path' in image:
                        # 本地图片
                        image_info = self.process_local_image(image['path'])
                        if image_info:
                            image['src'] = image_info['src']
                            processed_images.append(image)
                    elif 'url' in image:
                        # 远程图片
                        image_info = self.process_remote_image(image['url'])
                        if image_info:
                            image['src'] = image_info['src']
                            processed_images.append(image)
                
                # 去重：移除src重复的图片
                unique_images = []
                seen_srcs = set()
                for img in processed_images:
                    if img['src'] not in seen_srcs:
                        seen_srcs.add(img['src'])
                        unique_images.append(img)
                
                char_data['resources']['images'] = unique_images
            
            # 处理音频资源
            if 'audio' in resource_types:
                audio_files = char_data.get('resources', {}).get('audio', [])
                processed_audio = []
                
                for audio in audio_files:
                    if 'path' in audio:
                        # 本地音频
                        audio_info = self.process_local_audio(audio['path'])
                        if audio_info:
                            audio['src'] = audio_info['src']
                            processed_audio.append(audio)
                    elif 'url' in audio:
                        # 远程音频
                        audio_info = self.process_remote_audio(audio['url'])
                        if audio_info:
                            audio['src'] = audio_info['src']
                            processed_audio.append(audio)
                
                # 去重：移除src重复的音频
                unique_audio = []
                seen_srcs = set()
                for aud in processed_audio:
                    if aud['src'] not in seen_srcs:
                        seen_srcs.add(aud['src'])
                        unique_audio.append(aud)
                
                char_data['resources']['audio'] = unique_audio
            
            return True
            
        except Exception as e:
            logger.error(f"处理资源时出错: {str(e)}")
            logger.exception(e)
            return False
    
    def process_local_image(self, image_path: str) -> Optional[Dict[str, str]]:
        """处理本地图片
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            包含图片信息的字典，如果处理失败则返回None
        """
        try:
            if not os.path.exists(image_path):
                logger.warning(f"图片文件不存在: {image_path}")
                return None
            
            # 检查路径缓存
            with self._image_lock:
                if image_path in self._image_cache:
                    return self._image_cache[image_path]
            
            # 计算文件哈希值
            file_hash = self._compute_file_hash(image_path)
            if not file_hash:
                return None
            
            # 检查内容哈希缓存（去重）
            with self._image_lock:
                if file_hash in self._image_hash_cache:
                    image_info = {
                        'src': self._image_hash_cache[file_hash],
                        'original_path': image_path,
                        'hash': file_hash
                    }
                    self._image_cache[image_path] = image_info
                    return image_info
            
            # 获取文件扩展名
            _, ext = os.path.splitext(image_path)
            ext = ext.lower()
            if not ext or ext == '.':
                # 如果没有扩展名，根据文件内容推断
                ext = self._guess_image_extension(image_path)
            
            # 构建目标文件名和路径
            target_filename = f"{file_hash}{ext}"
            target_path = os.path.join(OUTPUT_IMAGES_DIR, target_filename)
            
            # 如果目标文件不存在，复制并优化图片
            if not os.path.exists(target_path):
                shutil.copy2(image_path, target_path)
                self._optimize_image(target_path)
                
                # 添加到已处理集合
                with self._image_lock:
                    self._processed_images.add(target_path)
            
            # 构建相对路径
            relative_path = f"images/{target_filename}"
            
            # 构建图片信息
            image_info = {
                'src': relative_path,
                'original_path': image_path,
                'hash': file_hash
            }
            
            # 更新缓存
            with self._image_lock:
                self._image_cache[image_path] = image_info
                self._image_hash_cache[file_hash] = relative_path
            
            return image_info
            
        except Exception as e:
            logger.error(f"处理本地图片时出错: {image_path}, 错误: {str(e)}")
            logger.exception(e)
            return None
    
    def process_remote_image(self, image_url: str) -> Optional[Dict[str, str]]:
        """处理远程图片
        
        Args:
            image_url: 图片URL
            
        Returns:
            包含图片信息的字典，如果处理失败则返回None
        """
        try:
            # 检查URL缓存
            with self._image_lock:
                if image_url in self._image_cache:
                    return self._image_cache[image_url]
            
            # 解析URL，获取文件名和扩展名
            parsed_url = urlparse(image_url)
            path = parsed_url.path
            _, ext = os.path.splitext(path)
            ext = ext.lower()
            
            # 如果没有有效的扩展名，使用.png作为默认值
            if not ext or ext == '.':
                ext = '.png'
            
            # 从URL生成哈希值
            url_hash = hashlib.md5(image_url.encode('utf-8')).hexdigest()
            
            # 构建目标文件名和路径
            target_filename = f"{url_hash}{ext}"
            target_path = os.path.join(OUTPUT_IMAGES_DIR, target_filename)
            
            # 如果目标文件不存在，下载并优化图片
            if not os.path.exists(target_path):
                if not self._download_file(image_url, target_path):
                    return None
                self._optimize_image(target_path)
                
                # 添加到已处理集合
                with self._image_lock:
                    self._processed_images.add(target_path)
                
                # 下载后计算实际内容哈希值（用于去重）
                if os.path.exists(target_path):
                    content_hash = self._compute_file_hash(target_path)
                    
                    # 如果有相同内容的文件，使用现有文件并删除当前文件
                    with self._image_lock:
                        if content_hash and content_hash in self._image_hash_cache:
                            # 已有相同内容的文件，删除当前文件
                            try:
                                os.remove(target_path)
                                return {
                                    'src': self._image_hash_cache[content_hash],
                                    'original_url': image_url,
                                    'hash': content_hash
                                }
                            except:
                                pass
                        elif content_hash:
                            # 添加到内容哈希缓存
                            relative_path = f"images/{target_filename}"
                            self._image_hash_cache[content_hash] = relative_path
            
            # 构建相对路径
            relative_path = f"images/{target_filename}"
            
            # 构建图片信息
            image_info = {
                'src': relative_path,
                'original_url': image_url,
                'hash': url_hash
            }
            
            # 更新缓存
            with self._image_lock:
                self._image_cache[image_url] = image_info
            
            return image_info
            
        except Exception as e:
            logger.error(f"处理远程图片时出错: {image_url}, 错误: {str(e)}")
            logger.exception(e)
            return None
    
    def process_local_audio(self, audio_path: str) -> Optional[Dict[str, str]]:
        """处理本地音频
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            包含音频信息的字典，如果处理失败则返回None
        """
        try:
            if not os.path.exists(audio_path):
                logger.warning(f"音频文件不存在: {audio_path}")
                return None
            
            # 检查路径缓存
            with self._audio_lock:
                if audio_path in self._audio_cache:
                    return self._audio_cache[audio_path]
            
            # 计算文件哈希值
            file_hash = self._compute_file_hash(audio_path)
            if not file_hash:
                return None
            
            # 检查内容哈希缓存（去重）
            with self._audio_lock:
                if file_hash in self._audio_hash_cache:
                    audio_info = {
                        'src': self._audio_hash_cache[file_hash],
                        'original_path': audio_path,
                        'hash': file_hash
                    }
                    self._audio_cache[audio_path] = audio_info
                    return audio_info
            
            # 获取文件扩展名
            _, ext = os.path.splitext(audio_path)
            ext = ext.lower()
            
            # 目标音频格式
            target_ext = f".{AUDIO_FORMAT}"
            
            # 构建目标文件名和路径
            target_filename = f"{file_hash}{target_ext}"
            target_path = os.path.join(OUTPUT_AUDIO_DIR, target_filename)
            
            # 如果目标文件不存在，复制并转换音频
            if not os.path.exists(target_path):
                # 如果扩展名相同，直接复制
                if ext == target_ext:
                    shutil.copy2(audio_path, target_path)
                else:
                    # 转换音频格式
                    self._convert_audio(audio_path, target_path)
                
                # 添加到已处理集合
                with self._audio_lock:
                    self._processed_audio.add(target_path)
            
            # 构建相对路径
            relative_path = f"audio/{target_filename}"
            
            # 构建音频信息
            audio_info = {
                'src': relative_path,
                'original_path': audio_path,
                'hash': file_hash
            }
            
            # 更新缓存
            with self._audio_lock:
                self._audio_cache[audio_path] = audio_info
                self._audio_hash_cache[file_hash] = relative_path
            
            return audio_info
            
        except Exception as e:
            logger.error(f"处理本地音频时出错: {audio_path}, 错误: {str(e)}")
            logger.exception(e)
            return None
    
    def process_remote_audio(self, audio_url: str) -> Optional[Dict[str, str]]:
        """处理远程音频
        
        Args:
            audio_url: 音频URL
            
        Returns:
            包含音频信息的字典，如果处理失败则返回None
        """
        try:
            # 检查URL缓存
            with self._audio_lock:
                if audio_url in self._audio_cache:
                    return self._audio_cache[audio_url]
            
            # 解析URL，获取文件名和扩展名
            parsed_url = urlparse(audio_url)
            path = parsed_url.path
            _, ext = os.path.splitext(path)
            ext = ext.lower()
            
            # 目标音频格式
            target_ext = f".{AUDIO_FORMAT}"
            
            # 从URL生成哈希值
            url_hash = hashlib.md5(audio_url.encode('utf-8')).hexdigest()
            
            # 临时文件和目标文件路径
            temp_path = os.path.join(OUTPUT_AUDIO_DIR, f"temp_{url_hash}{ext}")
            target_filename = f"{url_hash}{target_ext}"
            target_path = os.path.join(OUTPUT_AUDIO_DIR, target_filename)
            
            # 如果目标文件不存在，下载并转换音频
            if not os.path.exists(target_path):
                # 下载音频到临时文件
                if not self._download_file(audio_url, temp_path):
                    return None
                
                # 计算下载文件的内容哈希（用于去重）
                content_hash = self._compute_file_hash(temp_path)
                
                # 检查是否有相同内容的文件
                with self._audio_lock:
                    if content_hash and content_hash in self._audio_hash_cache:
                        # 已有相同内容的文件，删除临时文件
                        try:
                            os.remove(temp_path)
                            return {
                                'src': self._audio_hash_cache[content_hash],
                                'original_url': audio_url,
                                'hash': content_hash
                            }
                        except:
                            pass
                
                # 转换音频格式
                if ext == target_ext:
                    shutil.move(temp_path, target_path)
                else:
                    self._convert_audio(temp_path, target_path)
                    # 删除临时文件
                    try:
                        os.remove(temp_path)
                    except:
                        pass
                
                # 添加到已处理集合
                with self._audio_lock:
                    self._processed_audio.add(target_path)
                    
                    # 添加到内容哈希缓存
                    if content_hash:
                        relative_path = f"audio/{target_filename}"
                        self._audio_hash_cache[content_hash] = relative_path
            
            # 构建相对路径
            relative_path = f"audio/{target_filename}"
            
            # 构建音频信息
            audio_info = {
                'src': relative_path,
                'original_url': audio_url,
                'hash': url_hash
            }
            
            # 更新缓存
            with self._audio_lock:
                self._audio_cache[audio_url] = audio_info
            
            return audio_info
            
        except Exception as e:
            logger.error(f"处理远程音频时出错: {audio_url}, 错误: {str(e)}")
            logger.exception(e)
            return None
    
    def _compute_file_hash(self, file_path: str) -> Optional[str]:
        """计算文件的MD5哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件的MD5哈希值，如果失败则返回None
        """
        try:
            hasher = hashlib.md5()
            with open(file_path, 'rb') as f:
                # 分块读取，避免大文件占用太多内存
                for chunk in iter(lambda: f.read(4096), b''):
                    hasher.update(chunk)
            return hasher.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希值时出错: {file_path}, 错误: {str(e)}")
            return None
    
    def _download_file(self, url: str, target_path: str) -> bool:
        """下载文件
        
        Args:
            url: 文件URL
            target_path: 目标文件路径
            
        Returns:
            下载是否成功
        """
        try:
            # 创建请求头，模拟浏览器
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36'
            }
            
            # 创建请求
            req = urllib.request.Request(url, headers=headers)
            
            # 下载文件
            with urllib.request.urlopen(req, timeout=10) as response:
                with open(target_path, 'wb') as out_file:
                    shutil.copyfileobj(response, out_file)
            
            return True
        except Exception as e:
            logger.error(f"下载文件时出错: {url}, 错误: {str(e)}")
            return False
    
    def _optimize_image(self, image_path: str) -> bool:
        """优化图片（调整大小、压缩等）
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            优化是否成功
        """
        # 这里可以添加图片优化逻辑
        # 例如使用Pillow库调整图片大小、压缩等
        # 由于依赖问题，这里只是一个占位函数
        return True
    
    def _convert_audio(self, input_path: str, output_path: str) -> bool:
        """转换音频格式
        
        Args:
            input_path: 输入音频文件路径
            output_path: 输出音频文件路径
            
        Returns:
            转换是否成功
        """
        # 这里可以添加音频转换逻辑
        # 例如使用ffmpeg转换音频格式
        # 由于依赖问题，这里只是简单复制文件
        try:
            shutil.copy2(input_path, output_path)
            return True
        except Exception as e:
            logger.error(f"转换音频格式时出错: {input_path} -> {output_path}, 错误: {str(e)}")
            return False
    
    def _guess_image_extension(self, image_path: str) -> str:
        """根据文件内容推断图片扩展名
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            推断的扩展名
        """
        # 简单的文件头部签名检查
        signatures = {
            b'\xFF\xD8\xFF': '.jpg',
            b'\x89\x50\x4E\x47': '.png',
            b'\x47\x49\x46\x38': '.gif',
            b'<svg': '.svg'
        }
        
        try:
            with open(image_path, 'rb') as f:
                header = f.read(10)
                
            for sig, ext in signatures.items():
                if header.startswith(sig):
                    return ext
                
            # 如果无法识别，默认使用.png
            return '.png'
        except Exception:
            return '.png'
    
    def clear_cache(self) -> None:
        """清除资源缓存"""
        with self._image_lock:
            self._image_cache.clear()
        
        with self._audio_lock:
            self._audio_cache.clear()
    
    def get_stats(self) -> Dict[str, int]:
        """获取资源统计信息
        
        Returns:
            资源统计信息
        """
        with self._image_lock, self._audio_lock:
            return {
                'images': len(self._image_cache),
                'audio': len(self._audio_cache)
            } 