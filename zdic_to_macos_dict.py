#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
汉典数据转换为 macOS 词典格式
高性能模块化版本，使用多进程和流式处理
深度优化版本 - 确保所有内容完整转换
"""

import os
import sys
import time
import argparse
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加当前目录到模块搜索路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入模块
from modules.config import (
    setup_logging, ensure_dirs_exist, 
    INPUT_DIR, OUTPUT_DIR, OUTPUT_XML_DIR,
    DEFAULT_MAX_WORKERS, DICTIONARY_NAME
)
from modules.processor import (
    create_processor, process_all_characters
)
from modules.generator import generate_all_dictionary_files
from modules.validator import validate_xml_file
from modules.cache_manager import get_cache_manager
from modules.progress_manager import get_progress_manager

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='将汉典数据转换为macOS词典格式')
    
    # 基本参数
    parser.add_argument('--limit', type=int, default=0, help='限制处理的字符数量，0表示处理所有')
    parser.add_argument('--max-workers', type=int, default=DEFAULT_MAX_WORKERS, help='最大并行工作进程数')
    parser.add_argument('--output-dir', type=str, default=OUTPUT_DIR, help='输出目录路径')
    parser.add_argument('--log-level', type=str, choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='日志级别')
    
    # 操作模式
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument('--process-all', action='store_true', help='处理所有字符')
    mode_group.add_argument('--process-failed', action='store_true', help='重新处理之前失败的字符')
    mode_group.add_argument('--generate-only', action='store_true', help='仅生成词典文件')
    mode_group.add_argument('--validate-only', action='store_true', help='仅验证XML文件')
    mode_group.add_argument('--auto-repair', action='store_true', help='自动修复失败的字符并重新生成词典')
    mode_group.add_argument('--build-dict', action='store_true', help='生成并构建词典文件')
    mode_group.add_argument('--install-dict', action='store_true', help='构建并安装词典到系统')
    
    # 资源处理相关参数
    parser.add_argument('--skip-resources', action='store_true', help='跳过资源文件复制')
    parser.add_argument('--resource-types', type=str, nargs='+', default=['images', 'audio'], help='要复制的资源类型')
    
    # 词典生成参数
    parser.add_argument('--dict-version', type=str, default='1.0', help='词典版本号')
    
    # 深度优化参数
    parser.add_argument('--force-reprocess', action='store_true', help='强制重新处理所有字符，即使已经处理过')
    parser.add_argument('--validate-xml', action='store_true', help='在处理每个字符后验证XML文件')
    parser.add_argument('--detailed-log', action='store_true', help='输出详细日志')
    
    return parser.parse_args()

def build_dictionary(logger) -> bool:
    """构建词典
    
    Args:
        logger: 日志记录器
        
    Returns:
        是否成功构建
    """
    try:
        logger.info("开始构建词典...")
        
        # 检查Dictionary Development Kit是否存在
        ddk_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Dictionary Development Kit")
        if not os.path.exists(ddk_path):
            logger.error(f"Dictionary Development Kit不存在: {ddk_path}")
            return False
        
        # 检查build_dict.sh是否存在
        build_script = os.path.join(ddk_path, "bin", "build_dict.sh")
        if not os.path.exists(build_script):
            logger.error(f"构建脚本不存在: {build_script}")
            return False
        
        # 检查output/objects目录是否存在
        objects_dir = os.path.join(OUTPUT_DIR, "objects")
        if not os.path.exists(objects_dir):
            os.makedirs(objects_dir, exist_ok=True)
        
        # 检查combined.xml是否存在
        combined_xml = os.path.join(objects_dir, "combined.xml")
        if not os.path.exists(combined_xml):
            logger.error(f"合并的XML文件不存在: {combined_xml}")
            return False
        
        # 确保Info.plist存在
        info_plist = os.path.join(OUTPUT_DIR, "Info.plist")
        if not os.path.exists(info_plist):
            logger.error(f"Info.plist文件不存在: {info_plist}")
            return False
        
        # 确保CSS文件存在
        css_file = os.path.join(OUTPUT_DIR, "resources", "styles.css")
        if not os.path.exists(css_file):
            logger.error(f"CSS文件不存在: {css_file}")
            return False
        
        # 构建命令
        cmd = [
            build_script,
            "-v", "1",  # 详细级别
            "-s", "1",  # 排序级别
            "-c", "2",  # 压缩级别
            f'"{DICTIONARY_NAME}"',
            combined_xml,
            css_file,
            info_plist
        ]
        
        # 执行构建命令
        process = subprocess.Popen(
            " ".join(cmd),
            shell=True,
            cwd=OUTPUT_DIR,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 获取输出
        stdout, stderr = process.communicate()
        
        # 记录输出
        if stdout:
            logger.info(f"构建输出: {stdout}")
        if stderr:
            logger.error(f"构建错误: {stderr}")
        
        # 检查构建结果
        if process.returncode == 0:
            logger.info("词典构建成功")
            return True
        else:
            logger.error(f"词典构建失败，返回码: {process.returncode}")
            return False
        
    except Exception as e:
        logger.error(f"构建词典时出错: {str(e)}")
        logger.exception(e)
        return False

def install_dictionary(logger) -> bool:
    """安装词典到系统
    
    Args:
        logger: 日志记录器
        
    Returns:
        是否成功安装
    """
    try:
        logger.info("开始安装词典...")
        
        # 检查词典文件是否存在
        dict_file = os.path.join(OUTPUT_DIR, f"{DICTIONARY_NAME}.dictionary")
        if not os.path.exists(dict_file):
            logger.error(f"词典文件不存在: {dict_file}")
            return False
        
        # 安装目录
        install_dir = os.path.expanduser("~/Library/Dictionaries")
        
        # 确保安装目录存在
        os.makedirs(install_dir, exist_ok=True)
        
        # 构建安装命令
        cmd = [
            "cp", "-rf",
            dict_file,
            install_dir
        ]
        
        # 执行安装命令
        process = subprocess.Popen(
            " ".join(cmd),
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 获取输出
        stdout, stderr = process.communicate()
        
        # 记录输出
        if stdout:
            logger.info(f"安装输出: {stdout}")
        if stderr:
            logger.error(f"安装错误: {stderr}")
        
        # 检查安装结果
        if process.returncode == 0:
            logger.info(f"词典安装成功，安装位置: {os.path.join(install_dir, os.path.basename(dict_file))}")
            return True
        else:
            logger.error(f"词典安装失败，返回码: {process.returncode}")
            return False
        
    except Exception as e:
        logger.error(f"安装词典时出错: {str(e)}")
        logger.exception(e)
        return False

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置日志
    log_level = getattr(logging, args.log_level)
    logger = setup_logging(log_file=os.path.join(args.output_dir, "conversion.log"), console=True)
    logger.setLevel(log_level)
    
    # 确保目录存在
    ensure_dirs_exist()
    
    # 记录开始时间
    start_time = time.time()
    logger.info("开始转换...")
    logger.info(f"输入目录: {INPUT_DIR}")
    logger.info(f"输出目录: {args.output_dir}")
    logger.info(f"最大工作进程数: {args.max_workers}")
    
    try:
        # 创建处理器
        processor = create_processor(args.output_dir, args.max_workers)
        
        # 设置处理器选项
        processor.set_options(
            skip_resources=args.skip_resources,
            resource_types=args.resource_types,
            validate_xml=args.validate_xml,
            detailed_log=args.detailed_log
        )
        
        # 根据操作模式执行不同的操作
        if args.build_dict:
            # 生成并构建词典
            logger.info("生成并构建词典...")
            
            # 生成词典文件
            if not generate_all_dictionary_files(args.dict_version):
                logger.error("生成词典文件失败")
                return 1
            
            # 构建词典
            if build_dictionary(logger):
                logger.info("成功构建词典")
            else:
                logger.error("构建词典失败")
                return 1
            
        elif args.install_dict:
            # 构建并安装词典
            logger.info("构建并安装词典...")
            
            # 生成词典文件
            if not generate_all_dictionary_files(args.dict_version):
                logger.error("生成词典文件失败")
                return 1
            
            # 构建词典
            if not build_dictionary(logger):
                logger.error("构建词典失败")
                return 1
            
            # 安装词典
            if install_dictionary(logger):
                logger.info("成功安装词典")
            else:
                logger.error("安装词典失败")
                return 1
            
        elif args.generate_only:
            # 仅生成词典文件
            logger.info("生成词典文件...")
            
            # 查找已处理的XML文件
            xml_files = [f for f in os.listdir(OUTPUT_XML_DIR) if f.endswith('.xml')]
            processed_chars = [os.path.splitext(f)[0] for f in xml_files]
            
            if not processed_chars:
                logger.error("没有找到已处理的字符。请先处理字符。")
                return 1
            
            logger.info(f"找到 {len(processed_chars)} 个已处理的字符")
            
            # 生成词典文件
            if generate_all_dictionary_files(args.dict_version):
                logger.info("成功生成词典文件")
            else:
                logger.error("生成词典文件失败")
                return 1
        
        elif args.validate_only:
            # 仅验证XML文件
            logger.info("验证XML文件...")
            
            # 查找已处理的XML文件
            xml_files = [os.path.join(OUTPUT_XML_DIR, f) for f in os.listdir(OUTPUT_XML_DIR) if f.endswith('.xml')]
            
            if not xml_files:
                logger.error("没有找到XML文件。请先处理字符。")
                return 1
            
            logger.info(f"找到 {len(xml_files)} 个XML文件")
            
            # 验证XML文件
            valid_count = 0
            invalid_count = 0
            
            for xml_file in xml_files:
                if validate_xml_file(xml_file):
                    valid_count += 1
                else:
                    invalid_count += 1
                    logger.warning(f"无效的XML文件: {os.path.basename(xml_file)}")
            
            logger.info(f"验证完成: {valid_count} 个有效, {invalid_count} 个无效")
            
            if invalid_count > 0:
                logger.warning(f"发现 {invalid_count} 个无效的XML文件")
                return 1
        
        elif args.process_failed:
            # 重新处理之前失败的字符
            logger.info("重新处理之前失败的字符...")
            
            # 获取失败的字符
            progress_manager = get_progress_manager()
            failed_chars = progress_manager.get_failed_characters()
            
            if not failed_chars:
                logger.info("没有找到失败的字符。")
                return 0
            
            logger.info(f"找到 {len(failed_chars)} 个失败的字符")
            
            # 处理失败的字符
            stats = processor.process_characters(failed_chars)
            logger.info(f"处理完成: {stats['processed']} 成功, {stats['failed']} 失败")
        
        elif args.auto_repair:
            # 自动修复失败的字符并重新生成词典
            logger.info("开始自动修复流程...")
            
            # 第一步：处理所有字符
            logger.info("步骤1: 处理所有字符")
            stats = processor.process_all_characters(args.limit)
            logger.info(f"处理完成: {stats['processed']} 成功, {stats['failed']} 失败")
            
            # 第二步：如果有失败的字符，尝试修复
            if stats['failed'] > 0:
                progress_manager = get_progress_manager()
                failed_chars = progress_manager.get_failed_characters()
                logger.info(f"步骤2: 尝试修复 {len(failed_chars)} 个失败的字符")
                
                # 设置更详细的日志和验证选项
                processor.set_options(
                    skip_resources=args.skip_resources,
                    resource_types=args.resource_types,
                    validate_xml=True,
                    detailed_log=True
                )
                
                # 重新处理失败的字符
                repair_stats = processor.process_characters(failed_chars)
                logger.info(f"修复完成: {repair_stats['processed']} 成功, {repair_stats['failed']} 失败")
                
                # 更新总体统计
                stats['processed'] += repair_stats['processed']
                stats['failed'] = repair_stats['failed']
                
                # 如果仍有失败的字符，记录详细信息
                if repair_stats['failed'] > 0:
                    failed_chars = progress_manager.get_failed_characters()
                    logger.warning(f"无法修复的字符: {', '.join(failed_chars[:20])}" + 
                                  (f" 等 {len(failed_chars)} 个" if len(failed_chars) > 20 else ""))
            
            # 第三步：生成词典文件
            logger.info("步骤3: 生成词典文件")
            if generate_all_dictionary_files(args.dict_version):
                logger.info("成功生成词典文件")
            else:
                logger.error("生成词典文件失败")
                return 1
            
            # 输出总结
            logger.info(f"自动修复完成: 总共处理 {stats['processed'] + stats['failed']} 个字符，"
                       f"{stats['processed']} 个成功，{stats['failed']} 个失败")
            
            if stats['failed'] == 0:
                logger.info("全部字符处理成功！词典文件已生成。")
            else:
                logger.warning(f"有 {stats['failed']} 个字符无法处理，但词典文件已生成。")
        
        else:
            # 默认处理所有字符
            logger.info("处理所有字符...")
            
            # 如果强制重新处理，则清除进度
            if args.force_reprocess:
                logger.info("强制重新处理所有字符")
                progress_manager = get_progress_manager()
                progress_manager.clear_progress()
            
            # 处理字符
            stats = processor.process_all_characters(args.limit)
            logger.info(f"处理完成: {stats['processed']} 成功, {stats['failed']} 失败")
            
            # 如果有失败的字符，输出失败列表
            if stats['failed'] > 0:
                progress_manager = get_progress_manager()
                failed_chars = progress_manager.get_failed_characters()
                logger.warning(f"失败的字符: {', '.join(failed_chars[:20])}" + 
                              (f" 等 {len(failed_chars)} 个" if len(failed_chars) > 20 else ""))
        
        # 记录结束时间
        end_time = time.time()
        duration = end_time - start_time
        hours, remainder = divmod(duration, 3600)
        minutes, seconds = divmod(remainder, 60)
        logger.info(f"转换完成，耗时 {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断进程")
        return 1
    except Exception as e:
        logger.error(f"转换失败: {str(e)}")
        logger.exception(e)
        return 1

if __name__ == "__main__":
    sys.exit(main()) 