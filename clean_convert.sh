#!/bin/bash

# 汉典数据转换为 macOS 词典格式的自动化脚本
# 包含清理、转换、构建和安装功能

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 默认设置
AUTO_REPAIR=0
BUILD_DICT=0
INSTALL_DICT=0
CLEAN_OUTPUT=1
SKIP_CONVERT=0

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --auto-repair)
      AUTO_REPAIR=1
      shift
      ;;
    --build)
      BUILD_DICT=1
      shift
      ;;
    --install)
      INSTALL_DICT=1
      BUILD_DICT=1  # 安装前需要构建
      shift
      ;;
    --no-clean)
      CLEAN_OUTPUT=0
      shift
      ;;
    --skip-convert)
      SKIP_CONVERT=1
      shift
      ;;
    --help)
      echo "用法: $0 [选项]"
      echo "选项:"
      echo "  --auto-repair    自动修复失败的字符"
      echo "  --build          构建词典"
      echo "  --install        构建并安装词典到系统"
      echo "  --no-clean       不清除输出目录"
      echo "  --skip-convert   跳过转换步骤，仅执行构建或安装"
      echo "  --help           显示此帮助信息"
      exit 0
      ;;
    *)
      echo -e "${RED}未知选项: $key${NC}"
      exit 1
      ;;
  esac
done

# 记录开始时间
start_time=$(date +%s)

echo -e "${GREEN}检查Python环境...${NC}"
# 检查是否有虚拟环境
if [ -d "venv" ]; then
    echo "使用虚拟环境..."
    source venv/bin/activate
fi

echo -e "${GREEN}检查依赖...${NC}"
# 检查依赖是否安装
python -c "import bs4, lxml" 2>/dev/null || {
    echo -e "${YELLOW}安装必要的依赖...${NC}"
    pip install beautifulsoup4 lxml
}

# 如果需要清除输出目录
if [ $CLEAN_OUTPUT -eq 1 ]; then
    echo -e "${GREEN}清除所有历史数据...${NC}"
    # 清除输出目录
    rm -rf output/*
    
    echo -e "${GREEN}创建必要的目录...${NC}"
    # 创建必要的目录
    mkdir -p output/xml
    mkdir -p output/resources/images
    mkdir -p output/resources/audio
    
    echo -e "${GREEN}清除完成${NC}"
fi

# 执行转换步骤
if [ $SKIP_CONVERT -eq 0 ]; then
    echo -e "${GREEN}开始转换...${NC}"
    # 根据是否需要自动修复选择参数
    if [ $AUTO_REPAIR -eq 1 ]; then
        ./zdic_to_macos_dict.py --auto-repair
    else
        ./zdic_to_macos_dict.py --process-all
    fi
fi

# 构建词典
if [ $BUILD_DICT -eq 1 ]; then
    echo -e "${BLUE}开始构建词典...${NC}"
    ./zdic_to_macos_dict.py --build-dict
fi

# 安装词典
if [ $INSTALL_DICT -eq 1 ]; then
    echo -e "${BLUE}开始安装词典...${NC}"
    ./zdic_to_macos_dict.py --install-dict
fi

# 记录结束时间并计算耗时
end_time=$(date +%s)
duration=$((end_time - start_time))
minutes=$((duration / 60))
seconds=$((duration % 60))

echo -e "${GREEN}处理完成，总耗时: ${minutes}分${seconds}秒${NC}"
echo "结果保存在output目录中"

# 根据执行的操作给出不同的提示
if [ $INSTALL_DICT -eq 1 ]; then
    echo -e "${GREEN}词典已安装到系统，可以在macOS词典应用中使用${NC}"
elif [ $BUILD_DICT -eq 1 ]; then
    echo -e "${GREEN}词典已构建，可以在output目录中找到${NC}"
    echo "可以使用以下命令安装词典："
    echo "./clean_convert.sh --install --skip-convert"
else
    echo "可以使用以下命令构建词典："
    echo "./clean_convert.sh --build --no-clean --skip-convert"
    echo "或使用以下命令安装词典："
    echo "./clean_convert.sh --install --no-clean --skip-convert"
fi 