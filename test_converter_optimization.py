#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试转换器优化效果的脚本
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from modules.converter import Converter
from modules.extractor import Extractor
from modules.config import setup_logging

def test_xml_parsing():
    """测试XML解析功能"""
    print("=" * 60)
    print("测试XML解析功能")
    print("=" * 60)

    # 设置日志
    setup_logging(console=True, log_level='DEBUG')

    # 创建转换器
    converter = Converter()

    # 测试XML清理功能
    test_xml = '''<?xml version="1.0" encoding="UTF-8"?>
    <d:entry xmlns:d="http://www.apple.com/DTDs/DictionaryService-1.0.rng" id="test">
        <div>&lt;p&gt;这是一个测试&lt;/p&gt;</div>
    </d:entry>'''

    print("原始XML:")
    print(test_xml)
    print("\n清理后的XML:")
    cleaned = converter._clean_xml_string(test_xml)
    print(cleaned)

    print("\n修复后的XML:")
    fixed = converter._fix_xml_content(test_xml)
    print(fixed)

    # 测试HTML内容清理
    test_html = "&lt;p&gt;这是一个&amp;nbsp;测试&lt;/p&gt;"
    print(f"\n原始HTML: {test_html}")
    cleaned_html = converter._clean_html_content(test_html)
    print(f"清理后HTML: {cleaned_html}")

    # 测试HTML实体解码
    test_entities = "这是&lt;测试&gt;内容&amp;更多内容"
    print(f"\n原始实体: {test_entities}")
    decoded = converter._decode_html_entities(test_entities)
    print(f"解码后: {decoded}")

def test_character_conversion():
    """测试字符转换功能"""
    print("\n" + "=" * 60)
    print("测试字符转换功能")
    print("=" * 60)

    # 查找一个测试字符
    test_char_dir = None
    characters_dir = Path("characters")

    if characters_dir.exists():
        for char_dir in characters_dir.iterdir():
            if char_dir.is_dir():
                test_char_dir = char_dir
                break

    if not test_char_dir:
        print("未找到测试字符目录")
        return

    print(f"测试字符: {test_char_dir.name}")

    # 提取字符数据
    extractor = Extractor()
    html_file = test_char_dir / f"{test_char_dir.name}_original.html"
    if not html_file.exists():
        html_file = test_char_dir / f"{test_char_dir.name}.html"

    if not html_file.exists():
        print(f"未找到HTML文件: {html_file}")
        return

    char_data = extractor.extract_from_html(str(html_file))

    if not char_data:
        print("提取字符数据失败")
        return

    print(f"提取的数据键: {list(char_data.keys())}")

    # 转换为XML
    converter = Converter()
    xml_result = converter.convert_to_xml(char_data)

    if xml_result:
        print("XML转换成功")
        print(f"XML长度: {len(xml_result)} 字符")

        # 保存测试结果
        test_output_dir = Path("test_output")
        test_output_dir.mkdir(exist_ok=True)

        test_xml_file = test_output_dir / f"{test_char_dir.name}_test.xml"
        with open(test_xml_file, 'w', encoding='utf-8') as f:
            f.write(xml_result)

        print(f"测试XML已保存到: {test_xml_file}")

        # 显示XML的前500个字符
        print("\nXML内容预览:")
        print("-" * 40)
        print(xml_result[:500])
        if len(xml_result) > 500:
            print("...")
        print("-" * 40)
    else:
        print("XML转换失败")

def test_combined_xml_creation():
    """测试合并XML创建功能"""
    print("\n" + "=" * 60)
    print("测试合并XML创建功能")
    print("=" * 60)

    # 查找现有的XML文件
    xml_dir = Path("output/xml")
    if not xml_dir.exists():
        print("XML输出目录不存在")
        return

    xml_files = list(xml_dir.glob("*.xml"))[:5]  # 只测试前5个文件

    if not xml_files:
        print("未找到XML文件")
        return

    print(f"找到 {len(xml_files)} 个XML文件进行测试")

    # 读取XML内容
    entries = []
    for xml_file in xml_files:
        try:
            with open(xml_file, 'r', encoding='utf-8') as f:
                content = f.read()
                entries.append(content)
            print(f"成功读取: {xml_file.name}")
        except Exception as e:
            print(f"读取失败 {xml_file.name}: {e}")

    if not entries:
        print("没有成功读取的XML条目")
        return

    # 创建合并XML
    converter = Converter()
    test_output_dir = Path("test_output")
    test_output_dir.mkdir(exist_ok=True)

    combined_path = test_output_dir / "test_combined.xml"

    success = converter.create_dictionary_xml(entries, str(combined_path))

    if success:
        print(f"合并XML创建成功: {combined_path}")

        # 检查文件大小
        file_size = combined_path.stat().st_size
        print(f"文件大小: {file_size} 字节")

        # 显示文件开头
        with open(combined_path, 'r', encoding='utf-8') as f:
            content = f.read(1000)
            print("\n合并XML内容预览:")
            print("-" * 40)
            print(content)
            print("-" * 40)
    else:
        print("合并XML创建失败")

def main():
    """主函数"""
    print("汉典转macOS词典转换器优化测试")
    print("=" * 60)

    try:
        # 测试XML解析
        test_xml_parsing()

        # 测试字符转换
        test_character_conversion()

        # 测试合并XML创建
        test_combined_xml_creation()

        print("\n" + "=" * 60)
        print("所有测试完成")
        print("=" * 60)

    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
